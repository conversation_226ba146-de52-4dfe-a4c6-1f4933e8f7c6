<?php
require_once CONTROLLERS_PATH . '/BaseController.php';

class HomeController extends BaseController {
    
    public function index() {
        // If user is logged in, redirect to appropriate dashboard
        if (isLoggedIn()) {
            $role = getUserRole();
            switch ($role) {
                case 'admin':
                    $this->redirect('index.php?page=admin&action=dashboard');
                    break;
                case 'franchise':
                    $this->redirect('index.php?page=franchise&action=dashboard');
                    break;
                case 'user':
                    $this->redirect('index.php?page=user&action=dashboard');
                    break;
                default:
                    $this->redirect('index.php?page=auth&action=login');
            }
        }
        
        // Show landing page for guests
        $this->render('home/index', [
            'title' => 'Welcome to ShaktiPure MLM'
        ], null);
    }
    
    public function about() {
        $this->render('home/about', [
            'title' => 'About Us - ShaktiPure MLM'
        ]);
    }
    
    public function contact() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleContactForm();
        }
        
        $this->render('home/contact', [
            'title' => 'Contact Us - ShaktiPure MLM'
        ]);
    }
    
    private function handleContactForm() {
        $name = sanitizeInput($this->getPost('name'));
        $email = sanitizeInput($this->getPost('email'));
        $subject = sanitizeInput($this->getPost('subject'));
        $message = sanitizeInput($this->getPost('message'));
        
        // Validate
        $errors = $this->validate([
            'name' => $name,
            'email' => $email,
            'subject' => $subject,
            'message' => $message
        ], [
            'name' => 'required|min:2',
            'email' => 'required|email',
            'subject' => 'required|min:5',
            'message' => 'required|min:10'
        ]);
        
        if (empty($errors)) {
            // Here you would typically send an email or save to database
            $this->setFlash('success', 'Thank you for your message. We will get back to you soon!');
            $this->redirect('index.php?page=home&action=contact');
        } else {
            $this->setFlash('error', 'Please fix the errors and try again.');
        }
    }
    
    public function features() {
        $this->render('home/features', [
            'title' => 'Features - ShaktiPure MLM'
        ]);
    }
    
    public function compensation() {
        $this->render('home/compensation', [
            'title' => 'Compensation Plan - ShaktiPure MLM'
        ]);
    }
}
?>
