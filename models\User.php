<?php
require_once CONFIG_PATH . '/database.php';

class User extends BaseModel {
    protected $table = 'users';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Create a new user
     */
    public function createUser($data) {
        // Hash password
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        $data['created_at'] = date('Y-m-d H:i:s');
        
        return $this->create($data);
    }
    
    /**
     * Authenticate user login
     */
    public function authenticate($email, $password) {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        
        return false;
    }
    
    /**
     * Find user by email
     */
    public function findByEmail($email) {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch();
    }
    
    /**
     * Find user by sponsor ID
     */
    public function findBySponsorId($sponsorId) {
        return $this->findAll(['sponsor_id' => $sponsorId]);
    }
    
    /**
     * Get user's downline (direct referrals)
     */
    public function getDownline($userId) {
        $stmt = $this->db->prepare("
            SELECT * FROM users 
            WHERE parent_id = ? 
            ORDER BY position, created_at
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get user's genealogy tree
     */
    public function getGenealogy($userId, $levels = 5) {
        $tree = [];
        $this->buildTree($userId, $tree, 0, $levels);
        return $tree;
    }
    
    private function buildTree($userId, &$tree, $currentLevel, $maxLevels) {
        if ($currentLevel >= $maxLevels) return;
        
        $user = $this->find($userId);
        if (!$user) return;
        
        $tree[$userId] = $user;
        $tree[$userId]['level'] = $currentLevel;
        $tree[$userId]['children'] = [];
        
        // Get left and right children
        $children = $this->getDownline($userId);
        foreach ($children as $child) {
            $tree[$userId]['children'][] = $child['id'];
            $this->buildTree($child['id'], $tree, $currentLevel + 1, $maxLevels);
        }
    }
    
    /**
     * Find available position for new user
     */
    public function findAvailablePosition($parentId, $preferredPosition = null) {
        $stmt = $this->db->prepare("
            SELECT position FROM users 
            WHERE parent_id = ? 
            ORDER BY position
        ");
        $stmt->execute([$parentId]);
        $positions = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // If preferred position is available
        if ($preferredPosition && !in_array($preferredPosition, $positions)) {
            return $preferredPosition;
        }
        
        // Find first available position
        if (!in_array('left', $positions)) {
            return 'left';
        } elseif (!in_array('right', $positions)) {
            return 'right';
        }
        
        return null; // No position available
    }
    
    /**
     * Update user's BV (Business Volume)
     */
    public function updateBV($userId, $bv, $position) {
        $field = $position === 'left' ? 'left_bv' : 'right_bv';
        
        $stmt = $this->db->prepare("
            UPDATE users 
            SET $field = $field + ? 
            WHERE id = ?
        ");
        
        return $stmt->execute([$bv, $userId]);
    }
    
    /**
     * Calculate and update pairing income
     */
    public function calculatePairing($userId) {
        $user = $this->find($userId);
        if (!$user) return 0;
        
        $leftBV = $user['left_bv'];
        $rightBV = $user['right_bv'];
        
        // Calculate pairs (minimum of left and right BV)
        $newPairs = min($leftBV, $rightBV) / 100; // Assuming 100 BV = 1 pair
        $newPairs = floor($newPairs);
        
        $currentPairs = $user['total_pairs'];
        $additionalPairs = $newPairs - $currentPairs;
        
        if ($additionalPairs > 0) {
            // Update total pairs
            $this->update($userId, ['total_pairs' => $newPairs]);
            
            // Add pairing income to wallet
            $pairingAmount = $additionalPairs * PAIRING_AMOUNT;
            $this->addToWallet($userId, $pairingAmount, 'pairing', "Pairing income for $additionalPairs pairs");
            
            return $pairingAmount;
        }
        
        return 0;
    }
    
    /**
     * Add amount to user's wallet
     */
    public function addToWallet($userId, $amount, $type, $description) {
        require_once MODELS_PATH . '/Wallet.php';
        $wallet = new Wallet();
        
        return $wallet->addTransaction($userId, $amount, $type, $description);
    }
    
    /**
     * Get user's wallet balance
     */
    public function getWalletBalance($userId) {
        $stmt = $this->db->prepare("
            SELECT SUM(CASE WHEN type IN ('pairing', 'bonus', 'referral') THEN amount ELSE -amount END) as balance
            FROM wallet 
            WHERE user_id = ? AND status = 'completed'
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        
        return $result['balance'] ?? 0;
    }
    
    /**
     * Get user statistics
     */
    public function getUserStats($userId) {
        $stats = [];
        
        // Direct referrals count
        $stats['direct_referrals'] = $this->count(['sponsor_id' => $userId]);
        
        // Total downline count
        $stmt = $this->db->prepare("
            WITH RECURSIVE downline AS (
                SELECT id FROM users WHERE sponsor_id = ?
                UNION ALL
                SELECT u.id FROM users u
                INNER JOIN downline d ON u.sponsor_id = d.id
            )
            SELECT COUNT(*) as total FROM downline
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        $stats['total_downline'] = $result['total'] ?? 0;
        
        // Wallet balance
        $stats['wallet_balance'] = $this->getWalletBalance($userId);
        
        // Total purchases
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count, SUM(amount) as total
            FROM user_products 
            WHERE user_id = ? AND status = 'completed'
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        $stats['total_purchases'] = $result['count'] ?? 0;
        $stats['purchase_amount'] = $result['total'] ?? 0;
        
        return $stats;
    }
    
    /**
     * Get users by role
     */
    public function getUsersByRole($role) {
        return $this->findAll(['role' => $role], 'created_at DESC');
    }
    
    /**
     * Update user status
     */
    public function updateStatus($userId, $status) {
        return $this->update($userId, ['status' => $status]);
    }
    
    /**
     * Search users
     */
    public function searchUsers($query, $role = null) {
        $sql = "SELECT * FROM users WHERE (name LIKE ? OR email LIKE ?)";
        $params = ["%$query%", "%$query%"];
        
        if ($role) {
            $sql .= " AND role = ?";
            $params[] = $role;
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        return $this->query($sql, $params);
    }
}
?>
