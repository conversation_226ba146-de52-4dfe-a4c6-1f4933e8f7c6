<?php
/**
 * Database Setup Script
 * Run this file once to set up the database
 */

// Include configuration
require_once 'config/config.php';

// Database connection for setup
try {
    $pdo = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>ShaktiPure MLM Database Setup</h2>";
    
    // Read and execute SQL file
    $sql = file_get_contents('config/schema.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                echo "<p style='color: green;'>✓ Executed: " . substr($statement, 0, 50) . "...</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
                echo "<p style='color: red;'>Statement: " . substr($statement, 0, 100) . "...</p>";
            }
        }
    }
    
    echo "<h3 style='color: green;'>Database setup completed!</h3>";
    echo "<p><strong>Default Admin Login:</strong></p>";
    echo "<p>Email: <EMAIL></p>";
    echo "<p>Password: password</p>";
    echo "<p><a href='index.php'>Go to Website</a></p>";
    
    // Create uploads directory if it doesn't exist
    if (!is_dir('uploads')) {
        mkdir('uploads', 0755, true);
        echo "<p style='color: green;'>✓ Created uploads directory</p>";
    }
    
    // Create a default no-image placeholder
    $noImagePath = 'assets/images/no-image.png';
    if (!file_exists($noImagePath)) {
        // Create a simple placeholder image (you can replace this with an actual image)
        echo "<p style='color: orange;'>⚠ Please add a no-image.png file to assets/images/ directory</p>";
    }
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>Database connection failed!</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/config.php</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>ShaktiPure MLM Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        h2 { color: #333; }
        p { margin: 10px 0; }
    </style>
</head>
<body>
    <hr>
    <h3>Next Steps:</h3>
    <ol>
        <li>Update database credentials in <code>config/config.php</code> if needed</li>
        <li>Configure Razorpay keys in <code>config/config.php</code></li>
        <li>Set up email configuration for notifications</li>
        <li>Add product images to <code>uploads/</code> directory</li>
        <li>Delete this <code>setup.php</code> file for security</li>
    </ol>
    
    <h3>Features Included:</h3>
    <ul>
        <li>✓ User Registration with Sponsor System</li>
        <li>✓ Binary Tree Structure (Left/Right positioning)</li>
        <li>✓ Product Management</li>
        <li>✓ Pairing Income Calculation</li>
        <li>✓ Digital Wallet System</li>
        <li>✓ Withdrawal Management</li>
        <li>✓ Admin Dashboard</li>
        <li>✓ Franchise System</li>
        <li>✓ Razorpay Payment Integration (Ready)</li>
        <li>✓ Responsive Design</li>
    </ul>
</body>
</html>
