<?php
require_once CONFIG_PATH . '/database.php';

class Product extends BaseModel {
    protected $table = 'products';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Get all active products
     */
    public function getActiveProducts() {
        return $this->findAll(['status' => 'active'], 'name ASC');
    }
    
    /**
     * Create new product
     */
    public function createProduct($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        return $this->create($data);
    }
    
    /**
     * Update product
     */
    public function updateProduct($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');
        return $this->update($id, $data);
    }
    
    /**
     * Get product with image URL
     */
    public function getProductWithImage($id) {
        $product = $this->find($id);
        if ($product && $product['image']) {
            $product['image_url'] = SITE_URL . '/uploads/' . $product['image'];
        } else {
            $product['image_url'] = SITE_URL . '/assets/images/no-image.png';
        }
        return $product;
    }
    
    /**
     * Get products for catalog with pagination
     */
    public function getCatalogProducts($page = 1, $limit = 12) {
        $offset = ($page - 1) * $limit;
        
        $sql = "SELECT * FROM products WHERE status = 'active' ORDER BY name ASC LIMIT ? OFFSET ?";
        $products = $this->query($sql, [$limit, $offset]);
        
        // Add image URLs
        foreach ($products as &$product) {
            if ($product['image']) {
                $product['image_url'] = SITE_URL . '/uploads/' . $product['image'];
            } else {
                $product['image_url'] = SITE_URL . '/assets/images/no-image.png';
            }
        }
        
        return $products;
    }
    
    /**
     * Get total products count for pagination
     */
    public function getTotalProductsCount() {
        return $this->count(['status' => 'active']);
    }
    
    /**
     * Search products
     */
    public function searchProducts($query) {
        $sql = "SELECT * FROM products WHERE (name LIKE ? OR description LIKE ?) AND status = 'active' ORDER BY name ASC";
        $products = $this->query($sql, ["%$query%", "%$query%"]);
        
        // Add image URLs
        foreach ($products as &$product) {
            if ($product['image']) {
                $product['image_url'] = SITE_URL . '/uploads/' . $product['image'];
            } else {
                $product['image_url'] = SITE_URL . '/assets/images/no-image.png';
            }
        }
        
        return $products;
    }
    
    /**
     * Get product statistics
     */
    public function getProductStats($productId) {
        $stats = [];
        
        // Total sales count
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count, SUM(amount) as total
            FROM user_products 
            WHERE product_id = ? AND status = 'completed'
        ");
        $stmt->execute([$productId]);
        $result = $stmt->fetch();
        $stats['total_sales'] = $result['count'] ?? 0;
        $stats['total_revenue'] = $result['total'] ?? 0;
        
        // Total BV generated
        $stmt = $this->db->prepare("
            SELECT SUM(bv) as total_bv
            FROM user_products 
            WHERE product_id = ? AND status = 'completed'
        ");
        $stmt->execute([$productId]);
        $result = $stmt->fetch();
        $stats['total_bv'] = $result['total_bv'] ?? 0;
        
        return $stats;
    }
    
    /**
     * Get top selling products
     */
    public function getTopSellingProducts($limit = 10) {
        $sql = "
            SELECT p.*, COUNT(up.id) as sales_count, SUM(up.amount) as total_revenue
            FROM products p
            LEFT JOIN user_products up ON p.id = up.product_id AND up.status = 'completed'
            WHERE p.status = 'active'
            GROUP BY p.id
            ORDER BY sales_count DESC, total_revenue DESC
            LIMIT ?
        ";
        
        $products = $this->query($sql, [$limit]);
        
        // Add image URLs
        foreach ($products as &$product) {
            if ($product['image']) {
                $product['image_url'] = SITE_URL . '/uploads/' . $product['image'];
            } else {
                $product['image_url'] = SITE_URL . '/assets/images/no-image.png';
            }
        }
        
        return $products;
    }
    
    /**
     * Upload product image
     */
    public function uploadImage($file, $productId) {
        $uploadDir = ROOT_PATH . '/uploads/';
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        $maxSize = 2 * 1024 * 1024; // 2MB
        
        // Validate file
        if (!in_array($file['type'], $allowedTypes)) {
            throw new Exception('Invalid file type. Only JPG, PNG, and GIF are allowed.');
        }
        
        if ($file['size'] > $maxSize) {
            throw new Exception('File size too large. Maximum 2MB allowed.');
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'product_' . $productId . '_' . time() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Create upload directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Update product with image filename
            $this->update($productId, ['image' => $filename]);
            return $filename;
        } else {
            throw new Exception('Failed to upload image.');
        }
    }
    
    /**
     * Delete product image
     */
    public function deleteImage($productId) {
        $product = $this->find($productId);
        if ($product && $product['image']) {
            $filepath = ROOT_PATH . '/uploads/' . $product['image'];
            if (file_exists($filepath)) {
                unlink($filepath);
            }
            $this->update($productId, ['image' => null]);
        }
    }
    
    /**
     * Get products by price range
     */
    public function getProductsByPriceRange($minPrice, $maxPrice) {
        $sql = "SELECT * FROM products WHERE price BETWEEN ? AND ? AND status = 'active' ORDER BY price ASC";
        $products = $this->query($sql, [$minPrice, $maxPrice]);
        
        // Add image URLs
        foreach ($products as &$product) {
            if ($product['image']) {
                $product['image_url'] = SITE_URL . '/uploads/' . $product['image'];
            } else {
                $product['image_url'] = SITE_URL . '/assets/images/no-image.png';
            }
        }
        
        return $products;
    }
    
    /**
     * Get products by BV range
     */
    public function getProductsByBVRange($minBV, $maxBV) {
        $sql = "SELECT * FROM products WHERE bv BETWEEN ? AND ? AND status = 'active' ORDER BY bv ASC";
        $products = $this->query($sql, [$minBV, $maxBV]);
        
        // Add image URLs
        foreach ($products as &$product) {
            if ($product['image']) {
                $product['image_url'] = SITE_URL . '/uploads/' . $product['image'];
            } else {
                $product['image_url'] = SITE_URL . '/assets/images/no-image.png';
            }
        }
        
        return $products;
    }
    
    /**
     * Toggle product status
     */
    public function toggleStatus($productId) {
        $product = $this->find($productId);
        if ($product) {
            $newStatus = $product['status'] === 'active' ? 'inactive' : 'active';
            return $this->update($productId, ['status' => $newStatus]);
        }
        return false;
    }
}
?>
