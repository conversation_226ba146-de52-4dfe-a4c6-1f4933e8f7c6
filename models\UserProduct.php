<?php
require_once CONFIG_PATH . '/database.php';

class UserProduct extends BaseModel {
    protected $table = 'user_products';

    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Record product purchase
     */
    public function recordPurchase($userId, $productId, $amount, $bv, $paymentId = null) {
        $data = [
            'user_id' => $userId,
            'product_id' => $productId,
            'source' => 'purchase',
            'payment_id' => $paymentId,
            'amount' => $amount,
            'bv' => $bv,
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($data);
    }
    
    /**
     * Record product assignment by franchise
     */
    public function recordAssignment($userId, $productId, $assignedBy, $amount, $bv) {
        $data = [
            'user_id' => $userId,
            'product_id' => $productId,
            'assigned_by' => $assignedBy,
            'source' => 'assignment',
            'amount' => $amount,
            'bv' => $bv,
            'status' => 'completed',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $userProductId = $this->create($data);
        
        if ($userProductId) {
            // Process BV and commissions
            $this->processBVAndCommissions($userId, $bv, $userProductId);
        }
        
        return $userProductId;
    }
    
    /**
     * Complete purchase after payment verification
     */
    public function completePurchase($userProductId, $paymentId) {
        $userProduct = $this->find($userProductId);
        if (!$userProduct) {
            return false;
        }
        
        // Update status
        $updated = $this->update($userProductId, [
            'status' => 'completed',
            'payment_id' => $paymentId
        ]);
        
        if ($updated) {
            // Process BV and commissions
            $this->processBVAndCommissions(
                $userProduct['user_id'], 
                $userProduct['bv'], 
                $userProductId
            );
        }
        
        return $updated;
    }
    
    /**
     * Process BV and commissions
     */
    private function processBVAndCommissions($userId, $bv, $userProductId) {
        require_once MODELS_PATH . '/User.php';
        require_once MODELS_PATH . '/Commission.php';
        
        $userModel = new User();
        $commissionModel = new Commission();
        
        // Get user details
        $user = $userModel->find($userId);
        if (!$user || !$user['parent_id']) {
            return;
        }
        
        // Update parent's BV based on user's position
        $parentId = $user['parent_id'];
        $position = $user['position'];
        
        if ($position && in_array($position, ['left', 'right'])) {
            $userModel->updateBV($parentId, $bv, $position);
            
            // Calculate pairing income for parent
            $pairingIncome = $userModel->calculatePairing($parentId);
            
            if ($pairingIncome > 0) {
                // Record commission
                $commissionModel->recordCommission(
                    $parentId,
                    $userId,
                    'pairing',
                    $pairingIncome,
                    "Pairing income from user purchase",
                    $userProductId
                );
            }
            
            // Process upline BV (recursive)
            $this->processUplineBV($parentId, $bv, $position, $userProductId);
        }
    }
    
    /**
     * Process upline BV recursively
     */
    private function processUplineBV($userId, $bv, $originalPosition, $userProductId, $level = 1, $maxLevels = 10) {
        if ($level > $maxLevels) return;
        
        require_once MODELS_PATH . '/User.php';
        $userModel = new User();
        
        $user = $userModel->find($userId);
        if (!$user || !$user['parent_id']) {
            return;
        }
        
        $parentId = $user['parent_id'];
        $position = $user['position'];
        
        if ($position && in_array($position, ['left', 'right'])) {
            // Update parent's BV
            $userModel->updateBV($parentId, $bv, $position);
            
            // Calculate pairing income for parent
            $pairingIncome = $userModel->calculatePairing($parentId);
            
            if ($pairingIncome > 0) {
                require_once MODELS_PATH . '/Commission.php';
                $commissionModel = new Commission();
                
                // Record commission
                $commissionModel->recordCommission(
                    $parentId,
                    $userId,
                    'pairing',
                    $pairingIncome,
                    "Pairing income from downline (Level $level)",
                    $userProductId
                );
            }
            
            // Continue to next level
            $this->processUplineBV($parentId, $bv, $originalPosition, $userProductId, $level + 1, $maxLevels);
        }
    }
    
    /**
     * Get user's purchase history
     */
    public function getUserPurchases($userId, $limit = 50) {
        $stmt = $this->db->prepare("
            SELECT up.*, p.name as product_name, p.image
            FROM user_products up
            INNER JOIN products p ON up.product_id = p.id
            WHERE up.user_id = ?
            ORDER BY up.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get franchise assignment history
     */
    public function getFranchiseAssignments($franchiseId, $limit = 50) {
        $stmt = $this->db->prepare("
            SELECT up.*, p.name as product_name, u.name as user_name, u.email as user_email
            FROM user_products up
            INNER JOIN products p ON up.product_id = p.id
            INNER JOIN users u ON up.user_id = u.id
            WHERE up.assigned_by = ? AND up.source = 'assignment'
            ORDER BY up.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$franchiseId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get product sales statistics
     */
    public function getProductSalesStats($productId) {
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_sales,
                SUM(amount) as total_revenue,
                SUM(bv) as total_bv,
                COUNT(CASE WHEN source = 'purchase' THEN 1 END) as direct_sales,
                COUNT(CASE WHEN source = 'assignment' THEN 1 END) as franchise_assignments
            FROM user_products 
            WHERE product_id = ? AND status = 'completed'
        ");
        $stmt->execute([$productId]);
        return $stmt->fetch();
    }
    
    /**
     * Get user's total BV from purchases
     */
    public function getUserTotalBV($userId) {
        $stmt = $this->db->prepare("
            SELECT SUM(bv) as total_bv
            FROM user_products 
            WHERE user_id = ? AND status = 'completed'
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        return $result['total_bv'] ?? 0;
    }
    
    /**
     * Get recent purchases for dashboard
     */
    public function getRecentPurchases($limit = 10) {
        $stmt = $this->db->prepare("
            SELECT up.*, p.name as product_name, u.name as user_name
            FROM user_products up
            INNER JOIN products p ON up.product_id = p.id
            INNER JOIN users u ON up.user_id = u.id
            WHERE up.status = 'completed'
            ORDER BY up.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get sales by date range
     */
    public function getSalesByDateRange($dateFrom, $dateTo) {
        $stmt = $this->db->prepare("
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as sales_count,
                SUM(amount) as total_amount,
                SUM(bv) as total_bv
            FROM user_products 
            WHERE status = 'completed' 
                AND created_at BETWEEN ? AND ?
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$dateFrom, $dateTo]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get top selling products
     */
    public function getTopSellingProducts($limit = 10) {
        $stmt = $this->db->prepare("
            SELECT 
                p.id,
                p.name,
                p.price,
                p.bv,
                COUNT(up.id) as sales_count,
                SUM(up.amount) as total_revenue
            FROM products p
            INNER JOIN user_products up ON p.id = up.product_id
            WHERE up.status = 'completed'
            GROUP BY p.id, p.name, p.price, p.bv
            ORDER BY sales_count DESC, total_revenue DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Update purchase status
     */
    public function updateStatus($userProductId, $status) {
        return $this->update($userProductId, ['status' => $status]);
    }
    
    /**
     * Get purchase by payment ID
     */
    public function getByPaymentId($paymentId) {
        $stmt = $this->db->prepare("SELECT * FROM user_products WHERE payment_id = ?");
        $stmt->execute([$paymentId]);
        return $stmt->fetch();
    }
}
?>
