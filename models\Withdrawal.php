<?php
require_once CONFIG_PATH . '/database.php';

class Withdrawal extends BaseModel {
    protected $table = 'withdrawals';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Create withdrawal request
     */
    public function createRequest($userId, $amount, $bankDetails) {
        // Validate minimum withdrawal amount
        if ($amount < MIN_WITHDRAWAL) {
            throw new Exception('Minimum withdrawal amount is ₹' . MIN_WITHDRAWAL);
        }
        
        // Check user's available balance
        require_once MODELS_PATH . '/Wallet.php';
        $wallet = new Wallet();
        $balance = $wallet->getBalance($userId);
        
        if ($balance < $amount) {
            throw new Exception('Insufficient balance');
        }
        
        $data = [
            'user_id' => $userId,
            'amount' => $amount,
            'bank_details' => json_encode($bankDetails),
            'status' => 'pending',
            'requested_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($data);
    }
    
    /**
     * Get user's withdrawal requests
     */
    public function getUserWithdrawals($userId, $limit = 50) {
        $withdrawals = $this->findAll(['user_id' => $userId], 'requested_at DESC', $limit);
        
        // Decode bank details
        foreach ($withdrawals as &$withdrawal) {
            $withdrawal['bank_details'] = json_decode($withdrawal['bank_details'], true);
        }
        
        return $withdrawals;
    }
    
    /**
     * Get all pending withdrawals for admin
     */
    public function getPendingWithdrawals() {
        $stmt = $this->db->prepare("
            SELECT w.*, u.name as user_name, u.email as user_email
            FROM withdrawals w
            INNER JOIN users u ON w.user_id = u.id
            WHERE w.status = 'pending'
            ORDER BY w.requested_at ASC
        ");
        $stmt->execute();
        $withdrawals = $stmt->fetchAll();
        
        // Decode bank details
        foreach ($withdrawals as &$withdrawal) {
            $withdrawal['bank_details'] = json_decode($withdrawal['bank_details'], true);
        }
        
        return $withdrawals;
    }
    
    /**
     * Get all withdrawals with user details
     */
    public function getAllWithdrawals($status = null, $limit = 100) {
        $sql = "
            SELECT w.*, u.name as user_name, u.email as user_email
            FROM withdrawals w
            INNER JOIN users u ON w.user_id = u.id
        ";
        
        $params = [];
        
        if ($status) {
            $sql .= " WHERE w.status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY w.requested_at DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $withdrawals = $stmt->fetchAll();
        
        // Decode bank details
        foreach ($withdrawals as &$withdrawal) {
            $withdrawal['bank_details'] = json_decode($withdrawal['bank_details'], true);
        }
        
        return $withdrawals;
    }
    
    /**
     * Approve withdrawal request
     */
    public function approveWithdrawal($withdrawalId, $adminNotes = '') {
        $withdrawal = $this->find($withdrawalId);
        if (!$withdrawal || $withdrawal['status'] !== 'pending') {
            return false;
        }
        
        $data = [
            'status' => 'approved',
            'admin_notes' => $adminNotes,
            'processed_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->update($withdrawalId, $data);
    }
    
    /**
     * Reject withdrawal request
     */
    public function rejectWithdrawal($withdrawalId, $adminNotes = '') {
        $withdrawal = $this->find($withdrawalId);
        if (!$withdrawal || $withdrawal['status'] !== 'pending') {
            return false;
        }
        
        // Refund amount to user's wallet
        require_once MODELS_PATH . '/Wallet.php';
        $wallet = new Wallet();
        $wallet->addTransaction(
            $withdrawal['user_id'],
            $withdrawal['amount'],
            'bonus',
            'Withdrawal request rejected - amount refunded',
            $withdrawalId
        );
        
        $data = [
            'status' => 'rejected',
            'admin_notes' => $adminNotes,
            'processed_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->update($withdrawalId, $data);
    }
    
    /**
     * Mark withdrawal as processed
     */
    public function markAsProcessed($withdrawalId, $adminNotes = '') {
        $withdrawal = $this->find($withdrawalId);
        if (!$withdrawal || $withdrawal['status'] !== 'approved') {
            return false;
        }
        
        $data = [
            'status' => 'processed',
            'admin_notes' => $adminNotes,
            'processed_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->update($withdrawalId, $data);
    }
    
    /**
     * Get withdrawal statistics
     */
    public function getWithdrawalStats($userId = null) {
        $sql = "
            SELECT 
                status,
                COUNT(*) as count,
                SUM(amount) as total
            FROM withdrawals
        ";
        
        $params = [];
        
        if ($userId) {
            $sql .= " WHERE user_id = ?";
            $params[] = $userId;
        }
        
        $sql .= " GROUP BY status";
        
        return $this->query($sql, $params);
    }
    
    /**
     * Get monthly withdrawal report
     */
    public function getMonthlyWithdrawals($year = null, $month = null) {
        if (!$year) $year = date('Y');
        if (!$month) $month = date('m');
        
        $stmt = $this->db->prepare("
            SELECT 
                status,
                COUNT(*) as count,
                SUM(amount) as total
            FROM withdrawals 
            WHERE YEAR(requested_at) = ? AND MONTH(requested_at) = ?
            GROUP BY status
        ");
        $stmt->execute([$year, $month]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get pending withdrawal amount for user
     */
    public function getPendingAmount($userId) {
        $stmt = $this->db->prepare("
            SELECT SUM(amount) as total
            FROM withdrawals 
            WHERE user_id = ? AND status IN ('pending', 'approved')
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        
        return $result['total'] ?? 0;
    }
    
    /**
     * Get total processed withdrawals
     */
    public function getTotalProcessed($userId = null) {
        $sql = "
            SELECT SUM(amount) as total
            FROM withdrawals 
            WHERE status = 'processed'
        ";
        
        $params = [];
        
        if ($userId) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['total'] ?? 0;
    }
    
    /**
     * Get recent withdrawals for dashboard
     */
    public function getRecentWithdrawals($limit = 10) {
        $stmt = $this->db->prepare("
            SELECT w.*, u.name as user_name, u.email as user_email
            FROM withdrawals w
            INNER JOIN users u ON w.user_id = u.id
            ORDER BY w.requested_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        $withdrawals = $stmt->fetchAll();
        
        // Decode bank details
        foreach ($withdrawals as &$withdrawal) {
            $withdrawal['bank_details'] = json_decode($withdrawal['bank_details'], true);
        }
        
        return $withdrawals;
    }
    
    /**
     * Check if user can make withdrawal
     */
    public function canUserWithdraw($userId, $amount) {
        require_once MODELS_PATH . '/Wallet.php';
        $wallet = new Wallet();
        
        $balance = $wallet->getBalance($userId);
        $pendingAmount = $this->getPendingAmount($userId);
        $availableAmount = $balance - $pendingAmount;
        
        return [
            'can_withdraw' => $availableAmount >= $amount && $amount >= MIN_WITHDRAWAL,
            'balance' => $balance,
            'pending_amount' => $pendingAmount,
            'available_amount' => $availableAmount,
            'min_withdrawal' => MIN_WITHDRAWAL
        ];
    }
}
?>
