<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-credit-card"></i> Checkout
            </h1>
            <p class="text-muted">Complete your purchase securely</p>
        </div>
    </div>
    
    <div class="row">
        <!-- Order Summary -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> Order Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>BV</th>
                                    <th>Qty</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($cart as $item): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo $item['image_url']; ?>" 
                                                     alt="<?php echo htmlspecialchars($item['name']); ?>" 
                                                     class="rounded me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                <span><?php echo htmlspecialchars($item['name']); ?></span>
                                            </div>
                                        </td>
                                        <td><?php echo formatCurrency($item['price']); ?></td>
                                        <td><span class="badge bg-warning text-dark"><?php echo $item['bv']; ?></span></td>
                                        <td><?php echo $item['quantity']; ?></td>
                                        <td class="fw-bold"><?php echo formatCurrency($item['total']); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr class="table-light">
                                    <th colspan="4">Total</th>
                                    <th class="text-primary"><?php echo formatCurrency($cartTotal); ?></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Billing Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i> Billing Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Full Name</label>
                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['name']); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Phone</label>
                                <input type="tel" class="form-control" value="<?php echo htmlspecialchars($user['phone']); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">User ID</label>
                                <input type="text" class="form-control" value="<?php echo $user['id']; ?>" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Payment Section -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card"></i> Payment Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span><?php echo formatCurrency($cartTotal); ?></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Total BV:</span>
                        <span class="badge bg-warning text-dark"><?php echo number_format($totalBV); ?> BV</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Shipping:</span>
                        <span class="text-success">Free</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax:</span>
                        <span>Included</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <span class="h5">Total Amount:</span>
                        <span class="h5 text-success"><?php echo formatCurrency($cartTotal); ?></span>
                    </div>
                    
                    <!-- Payment Button -->
                    <div class="d-grid gap-2">
                        <button type="button" id="payNowBtn" class="btn btn-success btn-lg">
                            <i class="fas fa-lock"></i> Pay Securely with Razorpay
                        </button>
                        
                        <div class="text-center mt-3">
                            <img src="https://razorpay.com/assets/razorpay-glyph.svg" alt="Razorpay" style="height: 30px;">
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt text-success"></i> 
                                    Secure payment powered by Razorpay
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- MLM Benefits -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-gift"></i> MLM Benefits
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">You will earn:</small>
                        <div class="fw-bold text-warning"><?php echo number_format($totalBV); ?> BV</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Potential commission:</small>
                        <div class="fw-bold text-success">₹<?php echo number_format($totalBV * PAIRING_AMOUNT / 100); ?></div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">Your sponsor will earn:</small>
                        <div class="fw-bold text-info">5% referral bonus</div>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        This purchase contributes to your binary tree growth and commission earnings.
                    </small>
                </div>
            </div>
            
            <!-- Security Info -->
            <div class="card mt-3">
                <div class="card-body text-center">
                    <div class="mb-2">
                        <i class="fas fa-lock text-success fa-2x"></i>
                    </div>
                    <h6>Secure Payment</h6>
                    <small class="text-muted">
                        Your payment information is encrypted and secure. 
                        We never store your card details.
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Back to Cart -->
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="<?php echo SITE_URL; ?>?page=product&action=cart" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Cart
            </a>
        </div>
    </div>
</div>

<!-- Razorpay Script -->
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>

<script>
const razorpayKeyId = '<?php echo $razorpayKeyId; ?>';
const userDetails = {
    name: '<?php echo addslashes($user['name']); ?>',
    email: '<?php echo addslashes($user['email']); ?>',
    contact: '<?php echo addslashes($user['phone']); ?>'
};

document.getElementById('payNowBtn').addEventListener('click', function() {
    const button = this;
    const originalText = button.innerHTML;
    
    // Show loading state
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    button.disabled = true;
    
    // Create order
    fetch('<?php echo SITE_URL; ?>?page=product&action=processOrder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'csrf_token=<?php echo $csrf_token; ?>'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Initialize Razorpay
            const options = {
                "key": razorpayKeyId,
                "amount": data.amount,
                "currency": data.currency,
                "name": "ShaktiPure MLM",
                "description": "Product Purchase",
                "order_id": data.order_id,
                "handler": function (response) {
                    // Payment success
                    verifyPayment(response);
                },
                "prefill": userDetails,
                "theme": {
                    "color": "#28a745"
                },
                "modal": {
                    "ondismiss": function() {
                        // Reset button when modal is closed
                        button.innerHTML = originalText;
                        button.disabled = false;
                    }
                }
            };
            
            const rzp = new Razorpay(options);
            rzp.open();
            
            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;
            
        } else {
            showToast(data.message || 'Failed to create order', 'danger');
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred. Please try again.', 'danger');
        button.innerHTML = originalText;
        button.disabled = false;
    });
});

function verifyPayment(response) {
    // Show processing message
    showToast('Verifying payment...', 'info');
    
    fetch('<?php echo SITE_URL; ?>?page=payment&action=verify', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `razorpay_payment_id=${response.razorpay_payment_id}&razorpay_order_id=${response.razorpay_order_id}&razorpay_signature=${response.razorpay_signature}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            
            // Redirect after a short delay
            setTimeout(function() {
                window.location.href = data.redirect || '<?php echo SITE_URL; ?>?page=user&action=dashboard';
            }, 2000);
        } else {
            showToast(data.message || 'Payment verification failed', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Payment verification failed. Please contact support.', 'danger');
    });
}

// Show toast notification
function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    const toastElement = document.createElement('div');
    toastElement.innerHTML = toastHtml;
    toastContainer.appendChild(toastElement.firstElementChild);
    
    const toast = new bootstrap.Toast(toastElement.firstElementChild);
    toast.show();
    
    // Remove toast element after it's hidden
    toastElement.firstElementChild.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}
</script>
