<?php
require_once CONTROLLERS_PATH . '/BaseController.php';
require_once MODELS_PATH . '/UserProduct.php';
require_once MODELS_PATH . '/User.php';
require_once MODELS_PATH . '/Product.php';

class PaymentController extends BaseController {
    private $userProductModel;
    private $userModel;
    private $productModel;
    
    public function __construct() {
        parent::__construct();
        $this->requireLogin();
        
        $this->userProductModel = new UserProduct();
        $this->userModel = new User();
        $this->productModel = new Product();
    }
    
    public function verify() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->renderJSON(['success' => false, 'message' => 'Invalid request method']);
        }
        
        $razorpayPaymentId = $this->getPost('razorpay_payment_id');
        $razorpayOrderId = $this->getPost('razorpay_order_id');
        $razorpaySignature = $this->getPost('razorpay_signature');
        
        if (!$razorpayPaymentId || !$razorpayOrderId || !$razorpaySignature) {
            $this->renderJSON(['success' => false, 'message' => 'Missing payment details']);
        }
        
        // Get pending order from session
        $pendingOrder = $_SESSION['pending_order'] ?? null;
        
        if (!$pendingOrder || $pendingOrder['order_id'] !== $razorpayOrderId) {
            $this->renderJSON(['success' => false, 'message' => 'Invalid order']);
        }
        
        try {
            // Verify payment signature
            if ($this->verifyRazorpaySignature($razorpayOrderId, $razorpayPaymentId, $razorpaySignature)) {
                // Process the order
                $result = $this->processSuccessfulPayment($pendingOrder, $razorpayPaymentId);
                
                if ($result['success']) {
                    // Clear cart and pending order
                    unset($_SESSION['cart']);
                    unset($_SESSION['pending_order']);
                    
                    $this->renderJSON([
                        'success' => true,
                        'message' => 'Payment successful! Your order has been processed.',
                        'redirect' => 'index.php?page=user&action=dashboard'
                    ]);
                } else {
                    $this->renderJSON(['success' => false, 'message' => $result['message']]);
                }
            } else {
                $this->renderJSON(['success' => false, 'message' => 'Payment verification failed']);
            }
        } catch (Exception $e) {
            $this->renderJSON(['success' => false, 'message' => 'Payment processing error: ' . $e->getMessage()]);
        }
    }
    
    private function verifyRazorpaySignature($orderId, $paymentId, $signature) {
        // In a real implementation, you would verify the signature using Razorpay SDK
        // For demo purposes, we'll return true
        // 
        // $expectedSignature = hash_hmac('sha256', $orderId . "|" . $paymentId, RAZORPAY_KEY_SECRET);
        // return hash_equals($expectedSignature, $signature);
        
        return true; // Demo mode - always verify successfully
    }
    
    private function processSuccessfulPayment($pendingOrder, $paymentId) {
        try {
            $userId = $pendingOrder['user_id'];
            $cart = $pendingOrder['cart'];
            $totalAmount = $pendingOrder['amount'];
            $totalBV = $pendingOrder['total_bv'];
            
            // Process each product in the cart
            foreach ($cart as $item) {
                $productId = $item['product_id'];
                $quantity = $item['quantity'];
                $itemAmount = $item['price'] * $quantity;
                $itemBV = $item['bv'] * $quantity;
                
                // Record each product purchase
                for ($i = 0; $i < $quantity; $i++) {
                    $userProductId = $this->userProductModel->recordPurchase(
                        $userId,
                        $productId,
                        $item['price'],
                        $item['bv'],
                        $paymentId
                    );
                    
                    if ($userProductId) {
                        // Complete the purchase and trigger commission logic
                        $this->userProductModel->completePurchase($userProductId, $paymentId);
                    }
                }
            }
            
            $this->logActivity('Purchase completed', "Amount: ₹$totalAmount, BV: $totalBV, Payment ID: $paymentId");
            
            return [
                'success' => true,
                'message' => 'Order processed successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to process order: ' . $e->getMessage()
            ];
        }
    }
    
    public function success() {
        $paymentId = $this->getGet('payment_id');
        
        $this->render('payment/success', [
            'title' => 'Payment Successful - ShaktiPure MLM',
            'paymentId' => $paymentId
        ]);
    }
    
    public function failed() {
        $error = $this->getGet('error', 'Payment failed');
        
        $this->render('payment/failed', [
            'title' => 'Payment Failed - ShaktiPure MLM',
            'error' => $error
        ]);
    }
    
    public function webhook() {
        // Handle Razorpay webhooks for payment status updates
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            http_response_code(400);
            exit('Invalid JSON');
        }
        
        // Verify webhook signature
        $webhookSignature = $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] ?? '';
        $expectedSignature = hash_hmac('sha256', $input, RAZORPAY_KEY_SECRET);
        
        if (!hash_equals($expectedSignature, $webhookSignature)) {
            http_response_code(400);
            exit('Invalid signature');
        }
        
        // Process webhook event
        $event = $data['event'] ?? '';
        
        switch ($event) {
            case 'payment.captured':
                $this->handlePaymentCaptured($data['payload']['payment']['entity']);
                break;
                
            case 'payment.failed':
                $this->handlePaymentFailed($data['payload']['payment']['entity']);
                break;
                
            default:
                // Log unknown event
                error_log('Unknown Razorpay webhook event: ' . $event);
        }
        
        http_response_code(200);
        exit('OK');
    }
    
    private function handlePaymentCaptured($payment) {
        $paymentId = $payment['id'];
        $orderId = $payment['order_id'];
        $amount = $payment['amount'] / 100; // Convert from paise to rupees
        
        // Update payment status in database if needed
        // This is a backup in case the main verification flow fails
        
        $this->logActivity('Payment captured via webhook', "Payment ID: $paymentId, Amount: ₹$amount");
    }
    
    private function handlePaymentFailed($payment) {
        $paymentId = $payment['id'];
        $orderId = $payment['order_id'];
        $errorCode = $payment['error_code'] ?? 'UNKNOWN';
        $errorDescription = $payment['error_description'] ?? 'Payment failed';
        
        // Log failed payment
        $this->logActivity('Payment failed via webhook', "Payment ID: $paymentId, Error: $errorCode - $errorDescription");
    }
    
    public function history() {
        $userId = $_SESSION['user_id'];
        $page = max(1, (int)$this->getGet('page', 1));
        $limit = 20;
        
        // Get user's purchase history
        $purchases = $this->userProductModel->getUserPurchases($userId, $limit);
        
        // Calculate pagination (simplified)
        $totalPurchases = count($purchases);
        $pagination = $this->paginate($totalPurchases, $limit, $page);
        
        $this->render('payment/history', [
            'title' => 'Purchase History - ShaktiPure MLM',
            'purchases' => $purchases,
            'pagination' => $pagination
        ]);
    }
    
    public function invoice() {
        $userProductId = (int)$this->getGet('id');
        $userId = $_SESSION['user_id'];
        
        // Get purchase details
        $purchase = $this->userProductModel->find($userProductId);
        
        if (!$purchase || $purchase['user_id'] != $userId) {
            $this->setFlash('error', 'Purchase not found');
            $this->redirect('index.php?page=payment&action=history');
        }
        
        // Get product details
        $product = $this->productModel->find($purchase['product_id']);
        $user = $this->userModel->find($userId);
        
        $this->render('payment/invoice', [
            'title' => 'Invoice - ShaktiPure MLM',
            'purchase' => $purchase,
            'product' => $product,
            'user' => $user
        ], null); // No layout for invoice
    }
}
?>
