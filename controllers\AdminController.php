<?php
require_once CONTROLLERS_PATH . '/BaseController.php';
require_once MODELS_PATH . '/User.php';
require_once MODELS_PATH . '/Product.php';
require_once MODELS_PATH . '/Wallet.php';
require_once MODELS_PATH . '/UserProduct.php';
require_once MODELS_PATH . '/Withdrawal.php';
require_once MODELS_PATH . '/Commission.php';

class AdminController extends BaseController {
    private $userModel;
    private $productModel;
    private $walletModel;
    private $userProductModel;
    private $withdrawalModel;
    private $commissionModel;
    
    public function __construct() {
        parent::__construct();
        $this->requireRole('admin');
        
        $this->userModel = new User();
        $this->productModel = new Product();
        $this->walletModel = new Wallet();
        $this->userProductModel = new UserProduct();
        $this->withdrawalModel = new Withdrawal();
        $this->commissionModel = new Commission();
    }
    
    public function dashboard() {
        // Get dashboard statistics
        $stats = $this->getDashboardStats();
        
        $this->render('admin/dashboard', [
            'title' => 'Admin Dashboard - ShaktiPure MLM',
            'stats' => $stats
        ]);
    }
    
    public function users() {
        $page = max(1, (int)$this->getGet('page', 1));
        $search = $this->getGet('search', '');
        $role = $this->getGet('role', '');
        $limit = 20;
        
        if ($search) {
            $users = $this->userModel->searchUsers($search, $role);
            $totalUsers = count($users);
        } else {
            $conditions = [];
            if ($role) {
                $conditions['role'] = $role;
            }
            
            $totalUsers = $this->userModel->count($conditions);
            $offset = ($page - 1) * $limit;
            
            $users = $this->userModel->findAll($conditions, 'created_at DESC', $limit . ' OFFSET ' . $offset);
        }
        
        // Get user statistics for each user
        foreach ($users as &$user) {
            $user['stats'] = $this->userModel->getUserStats($user['id']);
        }
        
        $pagination = $this->paginate($totalUsers, $limit, $page);
        
        $this->render('admin/users', [
            'title' => 'User Management - Admin',
            'users' => $users,
            'pagination' => $pagination,
            'search' => $search,
            'role' => $role
        ]);
    }
    
    public function products() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $this->getPost('action');
            
            switch ($action) {
                case 'create':
                    $this->createProduct();
                    break;
                case 'update':
                    $this->updateProduct();
                    break;
                case 'delete':
                    $this->deleteProduct();
                    break;
                case 'toggle_status':
                    $this->toggleProductStatus();
                    break;
            }
        }
        
        $products = $this->productModel->findAll([], 'created_at DESC');
        
        // Get sales statistics for each product
        foreach ($products as &$product) {
            $product['stats'] = $this->userProductModel->getProductSalesStats($product['id']);
        }
        
        $this->render('admin/products', [
            'title' => 'Product Management - Admin',
            'products' => $products,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function withdrawals() {
        $status = $this->getGet('status', '');
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleWithdrawalAction();
        }
        
        $withdrawals = $this->withdrawalModel->getAllWithdrawals($status);
        
        $this->render('admin/withdrawals', [
            'title' => 'Withdrawal Management - Admin',
            'withdrawals' => $withdrawals,
            'status' => $status,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function commissions() {
        $type = $this->getGet('type', '');
        $dateFrom = $this->getGet('date_from', '');
        $dateTo = $this->getGet('date_to', '');
        
        $commissions = $this->commissionModel->getRecentCommissions(100);
        $summary = $this->commissionModel->getCommissionSummary($dateFrom, $dateTo);
        $topEarners = $this->commissionModel->getTopEarners($type, 10);
        
        $this->render('admin/commissions', [
            'title' => 'Commission Management - Admin',
            'commissions' => $commissions,
            'summary' => $summary,
            'topEarners' => $topEarners,
            'type' => $type,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo
        ]);
    }
    
    public function reports() {
        $reportType = $this->getGet('type', 'sales');
        $dateFrom = $this->getGet('date_from', date('Y-m-01'));
        $dateTo = $this->getGet('date_to', date('Y-m-d'));
        
        $data = [];
        
        switch ($reportType) {
            case 'sales':
                $data = $this->userProductModel->getSalesByDateRange($dateFrom, $dateTo);
                break;
            case 'withdrawals':
                $data = $this->withdrawalModel->getMonthlyWithdrawals(date('Y'), date('m'));
                break;
            case 'commissions':
                $data = $this->commissionModel->getCommissionSummary($dateFrom, $dateTo);
                break;
        }
        
        $this->render('admin/reports', [
            'title' => 'Reports - Admin',
            'reportType' => $reportType,
            'data' => $data,
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo
        ]);
    }
    
    public function settings() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->updateSettings();
        }
        
        // Get current settings (you would implement a Settings model)
        $settings = [
            'pairing_amount' => PAIRING_AMOUNT,
            'min_withdrawal' => MIN_WITHDRAWAL,
            'admin_commission' => ADMIN_COMMISSION
        ];
        
        $this->render('admin/settings', [
            'title' => 'System Settings - Admin',
            'settings' => $settings,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    private function getDashboardStats() {
        $stats = [];
        
        // User statistics
        $stats['total_users'] = $this->userModel->count(['role' => 'user']);
        $stats['total_franchises'] = $this->userModel->count(['role' => 'franchise']);
        $stats['active_users'] = $this->userModel->count(['role' => 'user', 'status' => 'active']);
        
        // Product statistics
        $stats['total_products'] = $this->productModel->count(['status' => 'active']);
        
        // Sales statistics
        $stats['total_sales'] = $this->userProductModel->count(['status' => 'completed']);
        $stats['total_revenue'] = $this->getTotalRevenue();
        
        // Commission statistics
        $stats['total_commissions'] = $this->getTotalCommissions();
        $stats['pending_withdrawals'] = $this->withdrawalModel->count(['status' => 'pending']);
        
        // Recent activities
        $stats['recent_users'] = $this->userModel->findAll(['role' => 'user'], 'created_at DESC', 5);
        $stats['recent_purchases'] = $this->userProductModel->getRecentPurchases(5);
        $stats['recent_withdrawals'] = $this->withdrawalModel->getRecentWithdrawals(5);
        
        return $stats;
    }
    
    private function getTotalRevenue() {
        $result = $this->userProductModel->query("
            SELECT SUM(amount) as total 
            FROM user_products 
            WHERE status = 'completed'
        ");
        return $result[0]['total'] ?? 0;
    }
    
    private function getTotalCommissions() {
        $result = $this->commissionModel->query("
            SELECT SUM(amount) as total 
            FROM commissions
        ");
        return $result[0]['total'] ?? 0;
    }
    
    private function createProduct() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $name = sanitizeInput($this->getPost('name'));
        $description = sanitizeInput($this->getPost('description'));
        $price = (float)$this->getPost('price');
        $bv = (float)$this->getPost('bv');
        
        $errors = $this->validate([
            'name' => $name,
            'price' => $price,
            'bv' => $bv
        ], [
            'name' => 'required|min:2',
            'price' => 'required|numeric',
            'bv' => 'required|numeric'
        ]);
        
        if (empty($errors)) {
            $productData = [
                'name' => $name,
                'description' => $description,
                'price' => $price,
                'bv' => $bv,
                'status' => 'active'
            ];
            
            $productId = $this->productModel->createProduct($productData);
            
            if ($productId && isset($_FILES['image']) && $_FILES['image']['error'] === 0) {
                try {
                    $this->productModel->uploadImage($_FILES['image'], $productId);
                } catch (Exception $e) {
                    $this->setFlash('error', 'Product created but image upload failed: ' . $e->getMessage());
                    return;
                }
            }
            
            $this->setFlash('success', 'Product created successfully');
            $this->logActivity('Product created', "Product: $name");
        } else {
            $this->setFlash('error', 'Please fix the errors and try again');
        }
        
        $this->redirect('index.php?page=admin&action=products');
    }
    
    private function updateProduct() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $productId = (int)$this->getPost('product_id');
        $name = sanitizeInput($this->getPost('name'));
        $description = sanitizeInput($this->getPost('description'));
        $price = (float)$this->getPost('price');
        $bv = (float)$this->getPost('bv');
        
        $productData = [
            'name' => $name,
            'description' => $description,
            'price' => $price,
            'bv' => $bv
        ];
        
        if ($this->productModel->updateProduct($productId, $productData)) {
            $this->setFlash('success', 'Product updated successfully');
            $this->logActivity('Product updated', "Product ID: $productId");
        } else {
            $this->setFlash('error', 'Failed to update product');
        }
        
        $this->redirect('index.php?page=admin&action=products');
    }
    
    private function deleteProduct() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $productId = (int)$this->getPost('product_id');
        
        if ($this->productModel->delete($productId)) {
            $this->setFlash('success', 'Product deleted successfully');
            $this->logActivity('Product deleted', "Product ID: $productId");
        } else {
            $this->setFlash('error', 'Failed to delete product');
        }
        
        $this->redirect('index.php?page=admin&action=products');
    }
    
    private function toggleProductStatus() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $productId = (int)$this->getPost('product_id');
        
        if ($this->productModel->toggleStatus($productId)) {
            $this->setFlash('success', 'Product status updated successfully');
            $this->logActivity('Product status toggled', "Product ID: $productId");
        } else {
            $this->setFlash('error', 'Failed to update product status');
        }
        
        $this->redirect('index.php?page=admin&action=products');
    }
    
    private function handleWithdrawalAction() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $action = $this->getPost('action');
        $withdrawalId = (int)$this->getPost('withdrawal_id');
        $adminNotes = sanitizeInput($this->getPost('admin_notes', ''));
        
        switch ($action) {
            case 'approve':
                if ($this->withdrawalModel->approveWithdrawal($withdrawalId, $adminNotes)) {
                    $this->setFlash('success', 'Withdrawal approved successfully');
                    $this->logActivity('Withdrawal approved', "Withdrawal ID: $withdrawalId");
                } else {
                    $this->setFlash('error', 'Failed to approve withdrawal');
                }
                break;
                
            case 'reject':
                if ($this->withdrawalModel->rejectWithdrawal($withdrawalId, $adminNotes)) {
                    $this->setFlash('success', 'Withdrawal rejected successfully');
                    $this->logActivity('Withdrawal rejected', "Withdrawal ID: $withdrawalId");
                } else {
                    $this->setFlash('error', 'Failed to reject withdrawal');
                }
                break;
                
            case 'process':
                if ($this->withdrawalModel->markAsProcessed($withdrawalId, $adminNotes)) {
                    $this->setFlash('success', 'Withdrawal marked as processed');
                    $this->logActivity('Withdrawal processed', "Withdrawal ID: $withdrawalId");
                } else {
                    $this->setFlash('error', 'Failed to process withdrawal');
                }
                break;
        }
        
        $this->redirect('index.php?page=admin&action=withdrawals');
    }
    
    private function updateSettings() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        // In a real application, you would update settings in database
        $this->setFlash('success', 'Settings updated successfully');
        $this->logActivity('Settings updated');
        $this->redirect('index.php?page=admin&action=settings');
    }
}
?>
