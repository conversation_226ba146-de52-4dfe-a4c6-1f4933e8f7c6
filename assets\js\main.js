/**
 * MLM Binary Plan - Main JavaScript File
 */

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Form validation
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        $(this).addClass('was-validated');
    });

    // Confirm delete actions
    $('.btn-delete').on('click', function(e) {
        if (!confirm('Are you sure you want to delete this item?')) {
            e.preventDefault();
        }
    });

    // Number formatting
    $('.currency').each(function() {
        var value = parseFloat($(this).text());
        if (!isNaN(value)) {
            $(this).text('₹' + value.toLocaleString('en-IN', {minimumFractionDigits: 2}));
        }
    });

    // Copy to clipboard functionality
    $('.copy-btn').on('click', function() {
        var target = $(this).data('target');
        var text = $(target).text() || $(target).val();
        
        navigator.clipboard.writeText(text).then(function() {
            showToast('Copied to clipboard!', 'success');
        });
    });

    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#searchTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // Tree node click handler
    $('.tree-node').on('click', function(e) {
        e.preventDefault();
        var userId = $(this).data('user-id');
        if (userId) {
            showUserDetails(userId);
        }
    });

    // Product quantity controls
    $('.qty-btn').on('click', function() {
        var input = $(this).siblings('input[type="number"]');
        var currentVal = parseInt(input.val()) || 0;
        var action = $(this).data('action');
        
        if (action === 'increase') {
            input.val(currentVal + 1);
        } else if (action === 'decrease' && currentVal > 1) {
            input.val(currentVal - 1);
        }
        
        updateProductTotal();
    });

    // Update product total
    function updateProductTotal() {
        var total = 0;
        $('.product-item').each(function() {
            var price = parseFloat($(this).find('.product-price').data('price')) || 0;
            var qty = parseInt($(this).find('.qty-input').val()) || 0;
            total += price * qty;
        });
        $('#totalAmount').text('₹' + total.toLocaleString('en-IN', {minimumFractionDigits: 2}));
    }
});

// Show toast notification
function showToast(message, type = 'info') {
    var toastHtml = `
        <div class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    var toastContainer = $('#toastContainer');
    if (toastContainer.length === 0) {
        $('body').append('<div id="toastContainer" class="toast-container position-fixed bottom-0 end-0 p-3"></div>');
        toastContainer = $('#toastContainer');
    }
    
    var toastElement = $(toastHtml);
    toastContainer.append(toastElement);
    
    var toast = new bootstrap.Toast(toastElement[0]);
    toast.show();
    
    toastElement.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

// Show user details modal
function showUserDetails(userId) {
    $.ajax({
        url: 'index.php?page=user&action=details',
        method: 'POST',
        data: { user_id: userId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                var user = response.user;
                var modalHtml = `
                    <div class="modal fade" id="userDetailsModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">User Details</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>Name:</strong> ${user.name}<br>
                                            <strong>Email:</strong> ${user.email}<br>
                                            <strong>Phone:</strong> ${user.phone || 'N/A'}<br>
                                            <strong>Role:</strong> ${user.role}<br>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Left BV:</strong> ${user.left_bv}<br>
                                            <strong>Right BV:</strong> ${user.right_bv}<br>
                                            <strong>Total Pairs:</strong> ${user.total_pairs}<br>
                                            <strong>Status:</strong> <span class="badge bg-${user.status === 'active' ? 'success' : 'secondary'}">${user.status}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#userDetailsModal').remove();
                $('body').append(modalHtml);
                $('#userDetailsModal').modal('show');
            }
        },
        error: function() {
            showToast('Error loading user details', 'danger');
        }
    });
}

// Binary tree functions
function expandTree(userId) {
    // Implementation for expanding tree nodes
    console.log('Expanding tree for user:', userId);
}

function collapseTree(userId) {
    // Implementation for collapsing tree nodes
    console.log('Collapsing tree for user:', userId);
}

// Razorpay payment integration
function initiatePayment(amount, orderId, userDetails) {
    var options = {
        "key": razorpayKeyId, // This will be set in the payment page
        "amount": amount * 100, // Amount in paise
        "currency": "INR",
        "name": "ShaktiPure MLM",
        "description": "Product Purchase",
        "order_id": orderId,
        "handler": function (response) {
            // Payment success
            $.ajax({
                url: 'index.php?page=payment&action=verify',
                method: 'POST',
                data: {
                    razorpay_payment_id: response.razorpay_payment_id,
                    razorpay_order_id: response.razorpay_order_id,
                    razorpay_signature: response.razorpay_signature
                },
                success: function(result) {
                    if (result.success) {
                        showToast('Payment successful!', 'success');
                        setTimeout(function() {
                            window.location.href = 'index.php?page=user&action=dashboard';
                        }, 2000);
                    } else {
                        showToast('Payment verification failed', 'danger');
                    }
                }
            });
        },
        "prefill": {
            "name": userDetails.name,
            "email": userDetails.email,
            "contact": userDetails.phone
        },
        "theme": {
            "color": "#007bff"
        }
    };
    
    var rzp = new Razorpay(options);
    rzp.open();
}

// Form helpers
function validateEmail(email) {
    var re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function validatePhone(phone) {
    var re = /^[6-9]\d{9}$/;
    return re.test(phone);
}

// Data table initialization
function initDataTable(tableId, options = {}) {
    if ($.fn.DataTable) {
        $(tableId).DataTable({
            responsive: true,
            pageLength: 25,
            order: [[0, 'desc']],
            ...options
        });
    }
}

// Export functions
function exportToCSV(tableId, filename) {
    var csv = [];
    var rows = document.querySelectorAll(tableId + " tr");
    
    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll("td, th");
        
        for (var j = 0; j < cols.length; j++) {
            row.push(cols[j].innerText);
        }
        
        csv.push(row.join(","));
    }
    
    downloadCSV(csv.join("\n"), filename);
}

function downloadCSV(csv, filename) {
    var csvFile = new Blob([csv], {type: "text/csv"});
    var downloadLink = document.createElement("a");
    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = "none";
    document.body.appendChild(downloadLink);
    downloadLink.click();
}
