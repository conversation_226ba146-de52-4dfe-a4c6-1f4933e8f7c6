<?php
require_once CONFIG_PATH . '/database.php';

class Wallet extends BaseModel {
    protected $table = 'wallet';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Add transaction to wallet
     */
    public function addTransaction($userId, $amount, $type, $description, $referenceId = null) {
        $data = [
            'user_id' => $userId,
            'amount' => $amount,
            'type' => $type,
            'description' => $description,
            'reference_id' => $referenceId,
            'status' => 'completed',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($data);
    }
    
    /**
     * Get user's wallet balance
     */
    public function getBalance($userId) {
        $stmt = $this->db->prepare("
            SELECT SUM(
                CASE 
                    WHEN type IN ('pairing', 'bonus', 'referral') THEN amount 
                    WHEN type IN ('withdrawal', 'debit') THEN -amount 
                    ELSE 0 
                END
            ) as balance
            FROM wallet 
            WHERE user_id = ? AND status = 'completed'
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        
        return max(0, $result['balance'] ?? 0);
    }
    
    /**
     * Get user's transaction history
     */
    public function getTransactionHistory($userId, $limit = 50, $offset = 0) {
        $stmt = $this->db->prepare("
            SELECT * FROM wallet 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$userId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get transactions by type
     */
    public function getTransactionsByType($userId, $type) {
        return $this->findAll([
            'user_id' => $userId,
            'type' => $type
        ], 'created_at DESC');
    }
    
    /**
     * Get total earnings by type
     */
    public function getTotalEarningsByType($userId, $type) {
        $stmt = $this->db->prepare("
            SELECT SUM(amount) as total
            FROM wallet 
            WHERE user_id = ? AND type = ? AND status = 'completed'
        ");
        $stmt->execute([$userId, $type]);
        $result = $stmt->fetch();
        
        return $result['total'] ?? 0;
    }
    
    /**
     * Get monthly earnings
     */
    public function getMonthlyEarnings($userId, $year = null, $month = null) {
        if (!$year) $year = date('Y');
        if (!$month) $month = date('m');
        
        $stmt = $this->db->prepare("
            SELECT 
                type,
                SUM(amount) as total,
                COUNT(*) as count
            FROM wallet 
            WHERE user_id = ? 
                AND YEAR(created_at) = ? 
                AND MONTH(created_at) = ?
                AND type IN ('pairing', 'bonus', 'referral')
                AND status = 'completed'
            GROUP BY type
        ");
        $stmt->execute([$userId, $year, $month]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get daily earnings for chart
     */
    public function getDailyEarnings($userId, $days = 30) {
        $stmt = $this->db->prepare("
            SELECT 
                DATE(created_at) as date,
                SUM(amount) as total
            FROM wallet 
            WHERE user_id = ? 
                AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                AND type IN ('pairing', 'bonus', 'referral')
                AND status = 'completed'
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ");
        $stmt->execute([$userId, $days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Process withdrawal request
     */
    public function processWithdrawal($userId, $amount, $bankDetails) {
        // Check if user has sufficient balance
        $balance = $this->getBalance($userId);
        if ($balance < $amount) {
            throw new Exception('Insufficient balance');
        }
        
        // Check minimum withdrawal amount
        if ($amount < MIN_WITHDRAWAL) {
            throw new Exception('Minimum withdrawal amount is ₹' . MIN_WITHDRAWAL);
        }
        
        // Create withdrawal request
        require_once MODELS_PATH . '/Withdrawal.php';
        $withdrawal = new Withdrawal();
        $withdrawalId = $withdrawal->createRequest($userId, $amount, $bankDetails);
        
        if ($withdrawalId) {
            // Add debit transaction (pending)
            $this->addTransaction(
                $userId, 
                $amount, 
                'withdrawal', 
                'Withdrawal request #' . $withdrawalId,
                $withdrawalId
            );
            
            return $withdrawalId;
        }
        
        return false;
    }
    
    /**
     * Get wallet summary
     */
    public function getWalletSummary($userId) {
        $summary = [];
        
        // Current balance
        $summary['balance'] = $this->getBalance($userId);
        
        // Total earnings
        $summary['total_pairing'] = $this->getTotalEarningsByType($userId, 'pairing');
        $summary['total_bonus'] = $this->getTotalEarningsByType($userId, 'bonus');
        $summary['total_referral'] = $this->getTotalEarningsByType($userId, 'referral');
        $summary['total_earnings'] = $summary['total_pairing'] + $summary['total_bonus'] + $summary['total_referral'];
        
        // Total withdrawals
        $summary['total_withdrawals'] = abs($this->getTotalEarningsByType($userId, 'withdrawal'));
        
        // Pending withdrawals
        $stmt = $this->db->prepare("
            SELECT SUM(amount) as total
            FROM withdrawals 
            WHERE user_id = ? AND status IN ('pending', 'approved')
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch();
        $summary['pending_withdrawals'] = $result['total'] ?? 0;
        
        // Available for withdrawal
        $summary['available_withdrawal'] = $summary['balance'] - $summary['pending_withdrawals'];
        
        return $summary;
    }
    
    /**
     * Get top earners
     */
    public function getTopEarners($limit = 10, $type = null) {
        $sql = "
            SELECT 
                u.id,
                u.name,
                u.email,
                SUM(w.amount) as total_earnings
            FROM users u
            INNER JOIN wallet w ON u.id = w.user_id
            WHERE w.status = 'completed' 
                AND w.type IN ('pairing', 'bonus', 'referral')
        ";
        
        $params = [];
        
        if ($type) {
            $sql .= " AND w.type = ?";
            $params[] = $type;
        }
        
        $sql .= "
            GROUP BY u.id, u.name, u.email
            ORDER BY total_earnings DESC
            LIMIT ?
        ";
        
        $params[] = $limit;
        
        return $this->query($sql, $params);
    }
    
    /**
     * Get transaction statistics
     */
    public function getTransactionStats($userId = null, $dateFrom = null, $dateTo = null) {
        $sql = "
            SELECT 
                type,
                COUNT(*) as count,
                SUM(amount) as total,
                AVG(amount) as average
            FROM wallet 
            WHERE status = 'completed'
        ";
        
        $params = [];
        
        if ($userId) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }
        
        if ($dateFrom) {
            $sql .= " AND created_at >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $sql .= " AND created_at <= ?";
            $params[] = $dateTo;
        }
        
        $sql .= " GROUP BY type ORDER BY total DESC";
        
        return $this->query($sql, $params);
    }
    
    /**
     * Update transaction status
     */
    public function updateTransactionStatus($transactionId, $status) {
        return $this->update($transactionId, ['status' => $status]);
    }
}
?>
