<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">
                <i class="fas fa-shopping-cart"></i> Shopping Cart
            </h1>
            <p class="text-muted">Review your selected products</p>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="<?php echo SITE_URL; ?>?page=product&action=catalog" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Continue Shopping
            </a>
        </div>
    </div>
    
    <?php if (!empty($cart)): ?>
        <!-- Cart Items -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list"></i> Cart Items (<?php echo count($cart); ?>)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Product</th>
                                        <th>Price</th>
                                        <th>BV</th>
                                        <th>Quantity</th>
                                        <th>Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cart as $item): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="<?php echo $item['image_url']; ?>" 
                                                         alt="<?php echo htmlspecialchars($item['name']); ?>" 
                                                         class="rounded me-3" style="width: 60px; height: 60px; object-fit: cover;">
                                                    <div>
                                                        <h6 class="mb-0"><?php echo htmlspecialchars($item['name']); ?></h6>
                                                        <small class="text-muted">Product ID: <?php echo $item['product_id']; ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="fw-bold"><?php echo formatCurrency($item['price']); ?></td>
                                            <td>
                                                <span class="badge bg-warning text-dark"><?php echo $item['bv']; ?> BV</span>
                                            </td>
                                            <td>
                                                <form method="POST" action="<?php echo SITE_URL; ?>?page=product&action=updateCart" class="d-flex align-items-center">
                                                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                    <input type="hidden" name="action" value="update">
                                                    <input type="hidden" name="product_id" value="<?php echo $item['product_id']; ?>">
                                                    
                                                    <div class="input-group" style="width: 120px;">
                                                        <button type="button" class="btn btn-outline-secondary btn-sm qty-btn" data-action="decrease">
                                                            <i class="fas fa-minus"></i>
                                                        </button>
                                                        <input type="number" name="quantity" class="form-control form-control-sm text-center qty-input" 
                                                               value="<?php echo $item['quantity']; ?>" min="1" max="99">
                                                        <button type="button" class="btn btn-outline-secondary btn-sm qty-btn" data-action="increase">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                    
                                                    <button type="submit" class="btn btn-sm btn-outline-primary ms-2">
                                                        <i class="fas fa-sync"></i>
                                                    </button>
                                                </form>
                                            </td>
                                            <td>
                                                <div class="fw-bold text-success"><?php echo formatCurrency($item['total']); ?></div>
                                                <small class="text-muted"><?php echo $item['total_bv']; ?> BV</small>
                                            </td>
                                            <td>
                                                <form method="POST" action="<?php echo SITE_URL; ?>?page=product&action=updateCart" class="d-inline">
                                                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                                    <input type="hidden" name="action" value="remove">
                                                    <input type="hidden" name="product_id" value="<?php echo $item['product_id']; ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                            onclick="return confirm('Remove this item from cart?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cart Summary -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-calculator"></i> Order Summary
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span class="fw-bold"><?php echo formatCurrency($cartTotal); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total BV:</span>
                            <span class="badge bg-warning text-dark"><?php echo number_format($totalBV); ?> BV</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span class="text-success">Free</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="h5">Total:</span>
                            <span class="h5 text-primary"><?php echo formatCurrency($cartTotal); ?></span>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="<?php echo SITE_URL; ?>?page=product&action=checkout" class="btn btn-success btn-lg">
                                <i class="fas fa-credit-card"></i> Proceed to Checkout
                            </a>
                            
                            <form method="POST" action="<?php echo SITE_URL; ?>?page=product&action=clearCart" class="d-inline">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <button type="submit" class="btn btn-outline-danger w-100" 
                                        onclick="return confirm('Clear all items from cart?')">
                                    <i class="fas fa-trash"></i> Clear Cart
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- MLM Benefits -->
                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-gift"></i> MLM Benefits
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <small class="text-muted">Business Volume (BV):</small>
                            <div class="fw-bold text-warning"><?php echo number_format($totalBV); ?> BV</div>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">Potential Pairing Income:</small>
                            <div class="fw-bold text-success">₹<?php echo number_format($totalBV * PAIRING_AMOUNT / 100); ?></div>
                        </div>
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i> 
                            This purchase will contribute to your binary tree and help generate pairing income.
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Empty Cart -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart text-muted fa-4x mb-4"></i>
                    <h3 class="text-muted mb-3">Your cart is empty</h3>
                    <p class="text-muted mb-4">Add some products to your cart to get started</p>
                    <a href="<?php echo SITE_URL; ?>?page=product&action=catalog" class="btn btn-primary btn-lg">
                        <i class="fas fa-shopping-bag"></i> Start Shopping
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Recommended Products -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-thumbs-up"></i> You might also like
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- This would show recommended products -->
                        <div class="col-12 text-center text-muted">
                            <p>Recommended products will appear here</p>
                            <a href="<?php echo SITE_URL; ?>?page=product&action=catalog" class="btn btn-outline-primary">
                                Browse All Products
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Quantity controls
    $('.qty-btn').on('click', function() {
        const input = $(this).siblings('.qty-input');
        const currentVal = parseInt(input.val()) || 1;
        const action = $(this).data('action');
        
        if (action === 'increase') {
            input.val(Math.min(99, currentVal + 1));
        } else if (action === 'decrease' && currentVal > 1) {
            input.val(currentVal - 1);
        }
    });
    
    // Auto-submit quantity changes after 2 seconds of inactivity
    let quantityTimeout;
    $('.qty-input').on('input', function() {
        const form = $(this).closest('form');
        
        clearTimeout(quantityTimeout);
        quantityTimeout = setTimeout(function() {
            form.submit();
        }, 2000);
    });
    
    // Prevent form submission on enter key in quantity input
    $('.qty-input').on('keypress', function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $(this).closest('form').submit();
        }
    });
});
</script>
