<?php
require_once CONTROLLERS_PATH . '/BaseController.php';
require_once MODELS_PATH . '/User.php';

class AuthController extends BaseController {
    private $userModel;
    
    public function __construct() {
        parent::__construct();
        $this->userModel = new User();
    }
    
    public function login() {
        // If already logged in, redirect to dashboard
        if (isLoggedIn()) {
            $this->redirectToDashboard();
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleLogin();
        }
        
        $this->render('auth/login', [
            'title' => 'Login - ShaktiPure MLM',
            'csrf_token' => $this->generateCSRF()
        ], null);
    }
    
    public function register() {
        // If already logged in, redirect to dashboard
        if (isLoggedIn()) {
            $this->redirectToDashboard();
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleRegister();
        }
        
        $this->render('auth/register', [
            'title' => 'Register - ShaktiPure MLM',
            'csrf_token' => $this->generateCSRF()
        ], null);
    }
    
    public function logout() {
        $this->logActivity('User logout');
        session_destroy();
        $this->setFlash('success', 'You have been logged out successfully');
        $this->redirect('index.php');
    }
    
    public function unauthorized() {
        $this->render('auth/unauthorized', [
            'title' => 'Access Denied - ShaktiPure MLM'
        ]);
    }
    
    private function handleLogin() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $email = sanitizeInput($this->getPost('email'));
        $password = $this->getPost('password');
        $remember = $this->getPost('remember');
        
        // Validate input
        $errors = $this->validate([
            'email' => $email,
            'password' => $password
        ], [
            'email' => 'required|email',
            'password' => 'required'
        ]);
        
        if (!empty($errors)) {
            $this->setFlash('error', 'Please fill all required fields correctly');
            return;
        }
        
        // Authenticate user
        $user = $this->userModel->authenticate($email, $password);
        
        if ($user) {
            // Set session data
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['last_activity'] = time();
            
            // Set remember me cookie if requested
            if ($remember) {
                $token = bin2hex(random_bytes(32));
                setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
                // You would typically store this token in database for security
            }
            
            $this->logActivity('User login', "Role: {$user['role']}");
            $this->setFlash('success', 'Welcome back, ' . $user['name'] . '!');
            $this->redirectToDashboard();
        } else {
            $this->setFlash('error', 'Invalid email or password');
        }
    }
    
    private function handleRegister() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $name = sanitizeInput($this->getPost('name'));
        $email = sanitizeInput($this->getPost('email'));
        $phone = sanitizeInput($this->getPost('phone'));
        $password = $this->getPost('password');
        $confirmPassword = $this->getPost('confirm_password');
        $sponsorId = sanitizeInput($this->getPost('sponsor_id'));
        $position = $this->getPost('position');
        $terms = $this->getPost('terms');
        
        // Validate input
        $errors = $this->validate([
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'password' => $password,
            'sponsor_id' => $sponsorId
        ], [
            'name' => 'required|min:2',
            'email' => 'required|email',
            'phone' => 'required|min:10',
            'password' => 'required|min:6',
            'sponsor_id' => 'required|numeric'
        ]);
        
        // Additional validations
        if ($password !== $confirmPassword) {
            $errors['confirm_password'] = 'Passwords do not match';
        }
        
        if (!$terms) {
            $errors['terms'] = 'You must accept the terms and conditions';
        }
        
        // Check if email already exists
        if ($this->userModel->findByEmail($email)) {
            $errors['email'] = 'Email already registered';
        }
        
        // Validate sponsor
        $sponsor = $this->userModel->find($sponsorId);
        if (!$sponsor) {
            $errors['sponsor_id'] = 'Invalid sponsor ID';
        }
        
        // Find parent and position
        $parentId = null;
        $assignedPosition = null;
        
        if ($sponsor) {
            if ($position && in_array($position, ['left', 'right'])) {
                // Check if requested position is available
                $availablePosition = $this->userModel->findAvailablePosition($sponsorId, $position);
                if ($availablePosition === $position) {
                    $parentId = $sponsorId;
                    $assignedPosition = $position;
                } else {
                    $errors['position'] = 'Requested position not available';
                }
            } else {
                // Auto-assign position
                $parentId = $this->findBestParent($sponsorId);
                $assignedPosition = $this->userModel->findAvailablePosition($parentId);
                
                if (!$assignedPosition) {
                    $errors['sponsor_id'] = 'No available positions in sponsor\'s tree';
                }
            }
        }
        
        if (!empty($errors)) {
            $this->data['errors'] = $errors;
            $this->data['old'] = $_POST;
            $this->setFlash('error', 'Please fix the errors and try again');
            return;
        }
        
        // Create user
        $userData = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'password' => $password,
            'sponsor_id' => $sponsorId,
            'parent_id' => $parentId,
            'position' => $assignedPosition,
            'role' => 'user',
            'status' => 'active'
        ];
        
        $userId = $this->userModel->createUser($userData);
        
        if ($userId) {
            $this->logActivity('User registration', "New user: $email");
            $this->setFlash('success', 'Registration successful! You can now login.');
            $this->redirect('index.php?page=auth&action=login');
        } else {
            $this->setFlash('error', 'Registration failed. Please try again.');
        }
    }
    
    private function findBestParent($sponsorId) {
        // Simple implementation: return sponsor as parent
        // In a more complex system, you might implement spillover logic
        return $sponsorId;
    }
    
    private function redirectToDashboard() {
        $role = getUserRole();
        switch ($role) {
            case 'admin':
                $this->redirect('index.php?page=admin&action=dashboard');
                break;
            case 'franchise':
                $this->redirect('index.php?page=franchise&action=dashboard');
                break;
            case 'user':
                $this->redirect('index.php?page=user&action=dashboard');
                break;
            default:
                $this->redirect('index.php');
        }
    }
    
    public function forgotPassword() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleForgotPassword();
        }
        
        $this->render('auth/forgot_password', [
            'title' => 'Forgot Password - ShaktiPure MLM',
            'csrf_token' => $this->generateCSRF()
        ], null);
    }
    
    private function handleForgotPassword() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $email = sanitizeInput($this->getPost('email'));
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->setFlash('error', 'Please enter a valid email address');
            return;
        }
        
        $user = $this->userModel->findByEmail($email);
        
        if ($user) {
            // Generate reset token (in a real application, you'd send this via email)
            $resetToken = bin2hex(random_bytes(32));
            // Store token in database with expiration
            // Send email with reset link
            
            $this->setFlash('success', 'Password reset instructions have been sent to your email');
        } else {
            // Don't reveal if email exists or not for security
            $this->setFlash('success', 'If the email exists, password reset instructions have been sent');
        }
        
        $this->redirect('index.php?page=auth&action=login');
    }
    
    public function checkSponsor() {
        $sponsorId = $this->getGet('sponsor_id');
        
        if ($sponsorId) {
            $sponsor = $this->userModel->find($sponsorId);
            if ($sponsor) {
                $this->renderJSON([
                    'success' => true,
                    'sponsor' => [
                        'id' => $sponsor['id'],
                        'name' => $sponsor['name'],
                        'email' => $sponsor['email']
                    ]
                ]);
            }
        }
        
        $this->renderJSON(['success' => false]);
    }
}
?>
