<?php
/**
 * Base Controller Class
 */

class BaseController {
    protected $data = [];
    
    public function __construct() {
        // Check session timeout
        $this->checkSessionTimeout();
    }
    
    /**
     * Render view with layout
     */
    protected function render($view, $data = [], $layout = 'layout') {
        // Merge data
        $this->data = array_merge($this->data, $data);
        
        // Extract data for view
        extract($this->data);
        
        // Start output buffering
        ob_start();
        
        // Include view file
        $viewFile = VIEWS_PATH . '/' . $view . '.php';
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            echo "<div class='alert alert-danger'>View file not found: $view</div>";
        }
        
        // Get view content
        $content = ob_get_clean();
        
        // Include layout
        if ($layout) {
            $layoutFile = VIEWS_PATH . '/' . $layout . '.php';
            if (file_exists($layoutFile)) {
                include $layoutFile;
            } else {
                echo $content;
            }
        } else {
            echo $content;
        }
    }
    
    /**
     * Render JSON response
     */
    protected function renderJSON($data) {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
    
    /**
     * Set flash message
     */
    protected function setFlash($type, $message) {
        $_SESSION[$type] = $message;
    }
    
    /**
     * Redirect to URL
     */
    protected function redirect($url) {
        header("Location: " . SITE_URL . "/" . $url);
        exit;
    }
    
    /**
     * Get POST data
     */
    protected function getPost($key = null, $default = null) {
        if ($key === null) {
            return $_POST;
        }
        return $_POST[$key] ?? $default;
    }
    
    /**
     * Get GET data
     */
    protected function getGet($key = null, $default = null) {
        if ($key === null) {
            return $_GET;
        }
        return $_GET[$key] ?? $default;
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCSRF() {
        $token = $this->getPost('csrf_token');
        if (!$token || !hash_equals($_SESSION['csrf_token'] ?? '', $token)) {
            $this->setFlash('error', 'Invalid security token');
            return false;
        }
        return true;
    }
    
    /**
     * Generate CSRF token
     */
    protected function generateCSRF() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Check if user is logged in
     */
    protected function requireLogin() {
        if (!isLoggedIn()) {
            $this->redirect('index.php?page=auth&action=login');
        }
    }
    
    /**
     * Check user role
     */
    protected function requireRole($role) {
        $this->requireLogin();
        if (getUserRole() !== $role) {
            $this->setFlash('error', 'Access denied');
            $this->redirect('index.php?page=auth&action=unauthorized');
        }
    }
    
    /**
     * Check session timeout
     */
    private function checkSessionTimeout() {
        if (isLoggedIn()) {
            $lastActivity = $_SESSION['last_activity'] ?? 0;
            if (time() - $lastActivity > SESSION_TIMEOUT) {
                session_destroy();
                $this->setFlash('error', 'Session expired. Please login again.');
                $this->redirect('index.php?page=auth&action=login');
            }
            $_SESSION['last_activity'] = time();
        }
    }
    
    /**
     * Validate input data
     */
    protected function validate($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? '';
            $ruleArray = explode('|', $rule);
            
            foreach ($ruleArray as $r) {
                if ($r === 'required' && empty($value)) {
                    $errors[$field] = ucfirst($field) . ' is required';
                    break;
                }
                
                if (strpos($r, 'min:') === 0 && strlen($value) < substr($r, 4)) {
                    $errors[$field] = ucfirst($field) . ' must be at least ' . substr($r, 4) . ' characters';
                    break;
                }
                
                if (strpos($r, 'max:') === 0 && strlen($value) > substr($r, 4)) {
                    $errors[$field] = ucfirst($field) . ' must not exceed ' . substr($r, 4) . ' characters';
                    break;
                }
                
                if ($r === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field] = 'Invalid email format';
                    break;
                }
                
                if ($r === 'numeric' && !is_numeric($value)) {
                    $errors[$field] = ucfirst($field) . ' must be a number';
                    break;
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * Upload file
     */
    protected function uploadFile($file, $directory = 'uploads', $allowedTypes = ['jpg', 'jpeg', 'png', 'gif']) {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            throw new Exception('No file uploaded');
        }
        
        $uploadDir = ROOT_PATH . '/' . $directory . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            throw new Exception('Invalid file type');
        }
        
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('File size too large');
        }
        
        $filename = uniqid() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return $filename;
        } else {
            throw new Exception('Failed to upload file');
        }
    }
    
    /**
     * Paginate results
     */
    protected function paginate($totalItems, $itemsPerPage = 20, $currentPage = 1) {
        $totalPages = ceil($totalItems / $itemsPerPage);
        $currentPage = max(1, min($totalPages, $currentPage));
        $offset = ($currentPage - 1) * $itemsPerPage;
        
        return [
            'total_items' => $totalItems,
            'items_per_page' => $itemsPerPage,
            'total_pages' => $totalPages,
            'current_page' => $currentPage,
            'offset' => $offset,
            'has_prev' => $currentPage > 1,
            'has_next' => $currentPage < $totalPages,
            'prev_page' => $currentPage - 1,
            'next_page' => $currentPage + 1
        ];
    }
    
    /**
     * Log activity
     */
    protected function logActivity($action, $details = '') {
        // Implementation for activity logging
        // This can be extended to log user activities
        error_log(date('Y-m-d H:i:s') . " - User: " . ($_SESSION['user_id'] ?? 'Guest') . " - Action: $action - Details: $details");
    }
}
?>
