<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">
                <i class="fas fa-money-bill-wave"></i> Withdrawal Management
            </h1>
            <p class="text-muted">Manage user withdrawal requests</p>
        </div>
        <div class="col-md-6">
            <!-- Filter -->
            <form method="GET" class="d-flex gap-2">
                <input type="hidden" name="page" value="admin">
                <input type="hidden" name="action" value="withdrawals">
                
                <select name="status" class="form-select" onchange="this.form.submit()">
                    <option value="">All Status</option>
                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="approved" <?php echo $status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                    <option value="processed" <?php echo $status === 'processed' ? 'selected' : ''; ?>>Processed</option>
                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                </select>
            </form>
        </div>
    </div>
    
    <!-- Withdrawals Table -->
    <div class="card table-custom">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Withdrawal Requests
            </h5>
            <div class="d-flex gap-2">
                <span class="badge bg-warning">Pending: <?php echo count(array_filter($withdrawals, fn($w) => $w['status'] === 'pending')); ?></span>
                <span class="badge bg-success">Approved: <?php echo count(array_filter($withdrawals, fn($w) => $w['status'] === 'approved')); ?></span>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (!empty($withdrawals)): ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Request ID</th>
                                <th>User</th>
                                <th>Amount</th>
                                <th>Bank Details</th>
                                <th>Status</th>
                                <th>Requested</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($withdrawals as $withdrawal): ?>
                                <tr>
                                    <td>
                                        <span class="fw-bold">#<?php echo $withdrawal['id']; ?></span>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($withdrawal['user_name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($withdrawal['user_email']); ?></small>
                                        </div>
                                    </td>
                                    <td class="fw-bold text-success">
                                        <?php echo formatCurrency($withdrawal['amount']); ?>
                                    </td>
                                    <td>
                                        <?php if ($withdrawal['bank_details']): ?>
                                            <button type="button" class="btn btn-sm btn-outline-info" 
                                                    onclick="showBankDetails(<?php echo htmlspecialchars(json_encode($withdrawal['bank_details'])); ?>)">
                                                <i class="fas fa-eye"></i> View Details
                                            </button>
                                        <?php else: ?>
                                            <small class="text-muted">No details</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $withdrawal['status'] === 'pending' ? 'warning' : 
                                                ($withdrawal['status'] === 'approved' ? 'success' : 
                                                ($withdrawal['status'] === 'processed' ? 'primary' : 'danger')); 
                                        ?>">
                                            <?php echo ucfirst($withdrawal['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo formatDate($withdrawal['requested_at']); ?></small>
                                        <?php if ($withdrawal['processed_at']): ?>
                                            <br><small class="text-success">Processed: <?php echo formatDate($withdrawal['processed_at']); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($withdrawal['status'] === 'pending'): ?>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-success" 
                                                        onclick="approveWithdrawal(<?php echo $withdrawal['id']; ?>)">
                                                    <i class="fas fa-check"></i> Approve
                                                </button>
                                                <button type="button" class="btn btn-outline-danger" 
                                                        onclick="rejectWithdrawal(<?php echo $withdrawal['id']; ?>)">
                                                    <i class="fas fa-times"></i> Reject
                                                </button>
                                            </div>
                                        <?php elseif ($withdrawal['status'] === 'approved'): ?>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="processWithdrawal(<?php echo $withdrawal['id']; ?>)">
                                                <i class="fas fa-check-double"></i> Mark Processed
                                            </button>
                                        <?php else: ?>
                                            <small class="text-muted">No actions</small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-money-bill-wave text-muted fa-3x mb-3"></i>
                    <h5 class="text-muted">No withdrawal requests found</h5>
                    <?php if ($status): ?>
                        <p class="text-muted">No withdrawals with status: <?php echo ucfirst($status); ?></p>
                        <a href="?page=admin&action=withdrawals" class="btn btn-outline-primary">
                            <i class="fas fa-times"></i> Clear Filter
                        </a>
                    <?php else: ?>
                        <p class="text-muted">Withdrawal requests will appear here</p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Bank Details Modal -->
<div class="modal fade" id="bankDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-university"></i> Bank Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="bankDetailsContent">
                <!-- Bank details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Action Modal -->
<div class="modal fade" id="actionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="actionModalTitle">
                    <i class="fas fa-edit"></i> Withdrawal Action
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                <input type="hidden" name="action" id="actionType">
                <input type="hidden" name="withdrawal_id" id="actionWithdrawalId">
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Admin Notes</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" 
                                  placeholder="Add notes about this action..."></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="actionButton">
                        <i class="fas fa-check"></i> Confirm
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showBankDetails(bankDetails) {
    let content = '<div class="row">';
    
    if (bankDetails.account_holder) {
        content += `<div class="col-12 mb-2"><strong>Account Holder:</strong> ${bankDetails.account_holder}</div>`;
    }
    if (bankDetails.account_number) {
        content += `<div class="col-12 mb-2"><strong>Account Number:</strong> ${bankDetails.account_number}</div>`;
    }
    if (bankDetails.bank_name) {
        content += `<div class="col-12 mb-2"><strong>Bank Name:</strong> ${bankDetails.bank_name}</div>`;
    }
    if (bankDetails.ifsc_code) {
        content += `<div class="col-12 mb-2"><strong>IFSC Code:</strong> ${bankDetails.ifsc_code}</div>`;
    }
    if (bankDetails.branch) {
        content += `<div class="col-12 mb-2"><strong>Branch:</strong> ${bankDetails.branch}</div>`;
    }
    
    content += '</div>';
    
    document.getElementById('bankDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('bankDetailsModal')).show();
}

function approveWithdrawal(withdrawalId) {
    document.getElementById('actionModalTitle').innerHTML = '<i class="fas fa-check text-success"></i> Approve Withdrawal';
    document.getElementById('actionType').value = 'approve';
    document.getElementById('actionWithdrawalId').value = withdrawalId;
    document.getElementById('actionButton').className = 'btn btn-success';
    document.getElementById('actionButton').innerHTML = '<i class="fas fa-check"></i> Approve';
    
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}

function rejectWithdrawal(withdrawalId) {
    document.getElementById('actionModalTitle').innerHTML = '<i class="fas fa-times text-danger"></i> Reject Withdrawal';
    document.getElementById('actionType').value = 'reject';
    document.getElementById('actionWithdrawalId').value = withdrawalId;
    document.getElementById('actionButton').className = 'btn btn-danger';
    document.getElementById('actionButton').innerHTML = '<i class="fas fa-times"></i> Reject';
    
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}

function processWithdrawal(withdrawalId) {
    document.getElementById('actionModalTitle').innerHTML = '<i class="fas fa-check-double text-primary"></i> Mark as Processed';
    document.getElementById('actionType').value = 'process';
    document.getElementById('actionWithdrawalId').value = withdrawalId;
    document.getElementById('actionButton').className = 'btn btn-primary';
    document.getElementById('actionButton').innerHTML = '<i class="fas fa-check-double"></i> Mark Processed';
    
    new bootstrap.Modal(document.getElementById('actionModal')).show();
}
</script>
