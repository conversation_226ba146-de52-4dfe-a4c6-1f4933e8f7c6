<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt"></i> Admin Dashboard
            </h1>
            <p class="text-muted">Welcome back, <?php echo $_SESSION['user_name']; ?>!</p>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card dashboard-card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo number_format($stats['total_users']); ?></h4>
                            <p class="card-text">Total Users</p>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <small class="text-white-50">
                        <i class="fas fa-check-circle"></i> <?php echo number_format($stats['active_users']); ?> Active
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card dashboard-card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo formatCurrency($stats['total_revenue']); ?></h4>
                            <p class="card-text">Total Revenue</p>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-rupee-sign"></i>
                        </div>
                    </div>
                    <small class="text-white-50">
                        <i class="fas fa-shopping-cart"></i> <?php echo number_format($stats['total_sales']); ?> Sales
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card dashboard-card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo formatCurrency($stats['total_commissions']); ?></h4>
                            <p class="card-text">Total Commissions</p>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                    <small class="text-white-50">
                        <i class="fas fa-handshake"></i> Paid to Users
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card dashboard-card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo number_format($stats['pending_withdrawals']); ?></h4>
                            <p class="card-text">Pending Withdrawals</p>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <small class="text-white-50">
                        <i class="fas fa-exclamation-triangle"></i> Needs Attention
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Secondary Stats -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-primary"><?php echo number_format($stats['total_products']); ?></h5>
                    <small class="text-muted">Active Products</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-success"><?php echo number_format($stats['total_franchises']); ?></h5>
                    <small class="text-muted">Franchises</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-info"><?php echo number_format($stats['total_sales']); ?></h5>
                    <small class="text-muted">Total Sales</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-warning"><?php echo date('d M Y'); ?></h5>
                    <small class="text-muted">Today's Date</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activities -->
    <div class="row g-4">
        <!-- Recent Users -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus text-primary"></i> Recent Users
                    </h5>
                    <a href="<?php echo SITE_URL; ?>?page=admin&action=users" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($stats['recent_users'])): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($stats['recent_users'] as $user): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($user['name']); ?></h6>
                                            <p class="mb-1 text-muted small"><?php echo htmlspecialchars($user['email']); ?></p>
                                            <small class="text-muted"><?php echo formatDate($user['created_at']); ?></small>
                                        </div>
                                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($user['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users text-muted fa-2x mb-2"></i>
                            <p class="text-muted">No recent users</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Purchases -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shopping-cart text-success"></i> Recent Purchases
                    </h5>
                    <a href="<?php echo SITE_URL; ?>?page=admin&action=reports" class="btn btn-sm btn-outline-success">
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($stats['recent_purchases'])): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($stats['recent_purchases'] as $purchase): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($purchase['product_name']); ?></h6>
                                            <p class="mb-1 text-muted small">by <?php echo htmlspecialchars($purchase['user_name']); ?></p>
                                            <small class="text-muted"><?php echo formatDate($purchase['created_at']); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-success"><?php echo formatCurrency($purchase['amount']); ?></div>
                                            <small class="text-muted">BV: <?php echo $purchase['bv']; ?></small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart text-muted fa-2x mb-2"></i>
                            <p class="text-muted">No recent purchases</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Withdrawals -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave text-warning"></i> Recent Withdrawals
                    </h5>
                    <a href="<?php echo SITE_URL; ?>?page=admin&action=withdrawals" class="btn btn-sm btn-outline-warning">
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($stats['recent_withdrawals'])): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($stats['recent_withdrawals'] as $withdrawal): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($withdrawal['user_name']); ?></h6>
                                            <p class="mb-1 text-muted small"><?php echo htmlspecialchars($withdrawal['user_email']); ?></p>
                                            <small class="text-muted"><?php echo formatDate($withdrawal['requested_at']); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold"><?php echo formatCurrency($withdrawal['amount']); ?></div>
                                            <span class="badge bg-<?php 
                                                echo $withdrawal['status'] === 'pending' ? 'warning' : 
                                                    ($withdrawal['status'] === 'approved' ? 'success' : 
                                                    ($withdrawal['status'] === 'processed' ? 'primary' : 'danger')); 
                                            ?>">
                                                <?php echo ucfirst($withdrawal['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-money-bill-wave text-muted fa-2x mb-2"></i>
                            <p class="text-muted">No recent withdrawals</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt text-primary"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=admin&action=products" class="btn btn-outline-primary w-100">
                                <i class="fas fa-plus"></i> Add Product
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=admin&action=users" class="btn btn-outline-success w-100">
                                <i class="fas fa-users"></i> Manage Users
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=admin&action=withdrawals" class="btn btn-outline-warning w-100">
                                <i class="fas fa-money-bill-wave"></i> Withdrawals
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=admin&action=reports" class="btn btn-outline-info w-100">
                                <i class="fas fa-chart-bar"></i> Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
