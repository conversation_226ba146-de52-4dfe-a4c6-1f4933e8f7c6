<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card" style="max-width: 500px;">
            <div class="auth-header">
                <h3 class="mb-0">
                    <i class="fas fa-user-plus"></i> Join ShaktiPure MLM
                </h3>
                <p class="mb-0 mt-2">Start your journey to success</p>
            </div>
            
            <div class="auth-body">
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user"></i> Full Name *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo $old['name'] ?? ''; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter your full name.
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i> Email Address *
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo $old['email'] ?? ''; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a valid email address.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">
                            <i class="fas fa-phone"></i> Phone Number *
                        </label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="<?php echo $old['phone'] ?? ''; ?>" required>
                        <div class="invalid-feedback">
                            Please enter your phone number.
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i> Password *
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Password must be at least 6 characters.
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock"></i> Confirm Password *
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <div class="invalid-feedback">
                                    Passwords must match.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="sponsor_id" class="form-label">
                                    <i class="fas fa-users"></i> Sponsor ID *
                                </label>
                                <input type="number" class="form-control" id="sponsor_id" name="sponsor_id" 
                                       value="<?php echo $old['sponsor_id'] ?? ''; ?>" required>
                                <div class="invalid-feedback">
                                    Please enter a valid sponsor ID.
                                </div>
                                <div id="sponsor-info" class="mt-2"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="position" class="form-label">
                                    <i class="fas fa-sitemap"></i> Position
                                </label>
                                <select class="form-select" id="position" name="position">
                                    <option value="">Auto Assign</option>
                                    <option value="left" <?php echo ($old['position'] ?? '') === 'left' ? 'selected' : ''; ?>>Left</option>
                                    <option value="right" <?php echo ($old['position'] ?? '') === 'right' ? 'selected' : ''; ?>>Right</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                        <label class="form-check-label" for="terms">
                            I agree to the <a href="#" target="_blank">Terms and Conditions</a> and <a href="#" target="_blank">Privacy Policy</a> *
                        </label>
                        <div class="invalid-feedback">
                            You must accept the terms and conditions.
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-gradient btn-lg">
                            <i class="fas fa-user-plus"></i> Create Account
                        </button>
                    </div>
                </form>
                
                <div class="text-center mt-4">
                    <p class="mb-0">
                        Already have an account? 
                        <a href="<?php echo SITE_URL; ?>?page=auth&action=login" class="text-decoration-none fw-bold">
                            <i class="fas fa-sign-in-alt"></i> Login Here
                        </a>
                    </p>
                </div>
                
                <hr class="my-4">
                
                <div class="text-center">
                    <a href="<?php echo SITE_URL; ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Toggle password visibility
            $('#togglePassword').click(function() {
                const passwordField = $('#password');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });
            
            // Check sponsor ID
            $('#sponsor_id').on('blur', function() {
                const sponsorId = $(this).val();
                const infoDiv = $('#sponsor-info');
                
                if (sponsorId) {
                    $.ajax({
                        url: '<?php echo SITE_URL; ?>?page=auth&action=checkSponsor',
                        method: 'GET',
                        data: { sponsor_id: sponsorId },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                infoDiv.html(`
                                    <div class="alert alert-success py-2">
                                        <small><i class="fas fa-check"></i> Sponsor: ${response.sponsor.name} (${response.sponsor.email})</small>
                                    </div>
                                `);
                            } else {
                                infoDiv.html(`
                                    <div class="alert alert-danger py-2">
                                        <small><i class="fas fa-times"></i> Invalid sponsor ID</small>
                                    </div>
                                `);
                            }
                        },
                        error: function() {
                            infoDiv.html(`
                                <div class="alert alert-warning py-2">
                                    <small><i class="fas fa-exclamation-triangle"></i> Could not verify sponsor ID</small>
                                </div>
                            `);
                        }
                    });
                } else {
                    infoDiv.empty();
                }
            });
            
            // Password confirmation validation
            $('#confirm_password').on('input', function() {
                const password = $('#password').val();
                const confirmPassword = $(this).val();
                
                if (password !== confirmPassword) {
                    this.setCustomValidity('Passwords do not match');
                } else {
                    this.setCustomValidity('');
                }
            });
            
            // Form validation
            $('.needs-validation').on('submit', function(e) {
                if (!this.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                $(this).addClass('was-validated');
            });
            
            // Auto-hide alerts
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
    </script>
</body>
</html>
