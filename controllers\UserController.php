<?php
require_once CONTROLLERS_PATH . '/BaseController.php';
require_once MODELS_PATH . '/User.php';
require_once MODELS_PATH . '/Product.php';
require_once MODELS_PATH . '/Wallet.php';
require_once MODELS_PATH . '/UserProduct.php';
require_once MODELS_PATH . '/Commission.php';
require_once MODELS_PATH . '/Withdrawal.php';

class UserController extends BaseController {
    private $userModel;
    private $productModel;
    private $walletModel;
    private $userProductModel;
    private $commissionModel;
    private $withdrawalModel;
    
    public function __construct() {
        parent::__construct();
        $this->requireRole('user');
        
        $this->userModel = new User();
        $this->productModel = new Product();
        $this->walletModel = new Wallet();
        $this->userProductModel = new UserProduct();
        $this->commissionModel = new Commission();
        $this->withdrawalModel = new Withdrawal();
    }
    
    public function dashboard() {
        $userId = $_SESSION['user_id'];
        
        // Get user statistics
        $stats = $this->userModel->getUserStats($userId);
        $user = $this->userModel->find($userId);
        $walletSummary = $this->walletModel->getWalletSummary($userId);
        
        // Get recent activities
        $recentPurchases = $this->userProductModel->getUserPurchases($userId, 5);
        $recentCommissions = $this->commissionModel->getUserCommissions($userId, 5);
        $recentTransactions = $this->walletModel->getTransactionHistory($userId, 5);
        
        // Get downline users
        $downline = $this->userModel->getDownline($userId);
        
        $this->render('user/dashboard', [
            'title' => 'User Dashboard - ShaktiPure MLM',
            'user' => $user,
            'stats' => $stats,
            'walletSummary' => $walletSummary,
            'recentPurchases' => $recentPurchases,
            'recentCommissions' => $recentCommissions,
            'recentTransactions' => $recentTransactions,
            'downline' => $downline
        ]);
    }
    
    public function genealogy() {
        $userId = $_SESSION['user_id'];
        $viewUserId = $this->getGet('user_id', $userId);
        
        // Get genealogy tree
        $tree = $this->userModel->getGenealogy($viewUserId, 5);
        $user = $this->userModel->find($viewUserId);
        
        $this->render('user/genealogy', [
            'title' => 'Genealogy Tree - ShaktiPure MLM',
            'tree' => $tree,
            'user' => $user,
            'viewUserId' => $viewUserId
        ]);
    }
    
    public function wallet() {
        $userId = $_SESSION['user_id'];
        $page = max(1, (int)$this->getGet('page', 1));
        $type = $this->getGet('type', '');
        $limit = 20;
        
        // Get wallet summary
        $walletSummary = $this->walletModel->getWalletSummary($userId);
        
        // Get transaction history
        $offset = ($page - 1) * $limit;
        $transactions = $this->walletModel->getTransactionHistory($userId, $limit, $offset);
        
        // Filter by type if specified
        if ($type) {
            $transactions = array_filter($transactions, function($t) use ($type) {
                return $t['type'] === $type;
            });
        }
        
        // Get monthly earnings
        $monthlyEarnings = $this->walletModel->getMonthlyEarnings($userId);
        
        // Get daily earnings for chart
        $dailyEarnings = $this->walletModel->getDailyEarnings($userId, 30);
        
        $totalTransactions = $this->walletModel->count(['user_id' => $userId]);
        $pagination = $this->paginate($totalTransactions, $limit, $page);
        
        $this->render('user/wallet', [
            'title' => 'My Wallet - ShaktiPure MLM',
            'walletSummary' => $walletSummary,
            'transactions' => $transactions,
            'monthlyEarnings' => $monthlyEarnings,
            'dailyEarnings' => $dailyEarnings,
            'pagination' => $pagination,
            'type' => $type
        ]);
    }
    
    public function withdraw() {
        $userId = $_SESSION['user_id'];
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleWithdrawRequest();
        }
        
        // Get withdrawal capability
        $amount = (float)$this->getGet('amount', 0);
        $withdrawalCheck = $this->withdrawalModel->canUserWithdraw($userId, $amount);
        
        // Get withdrawal history
        $withdrawalHistory = $this->withdrawalModel->getUserWithdrawals($userId);
        
        $this->render('user/withdraw', [
            'title' => 'Withdraw Funds - ShaktiPure MLM',
            'withdrawalCheck' => $withdrawalCheck,
            'withdrawalHistory' => $withdrawalHistory,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function commissions() {
        $userId = $_SESSION['user_id'];
        $type = $this->getGet('type', '');
        $page = max(1, (int)$this->getGet('page', 1));
        $limit = 20;
        
        // Get commission statistics
        $stats = $this->commissionModel->getCommissionStats($userId);
        
        // Get commission history
        $offset = ($page - 1) * $limit;
        $commissions = $this->commissionModel->getUserCommissions($userId, $limit);
        
        // Filter by type if specified
        if ($type) {
            $commissions = array_filter($commissions, function($c) use ($type) {
                return $c['type'] === $type;
            });
        }
        
        // Get monthly commissions
        $monthlyCommissions = $this->commissionModel->getMonthlyCommissions($userId);
        
        // Get daily commissions for chart
        $dailyCommissions = $this->commissionModel->getDailyCommissions($userId, 30);
        
        $totalCommissions = $this->commissionModel->count(['user_id' => $userId]);
        $pagination = $this->paginate($totalCommissions, $limit, $page);
        
        $this->render('user/commissions', [
            'title' => 'My Commissions - ShaktiPure MLM',
            'stats' => $stats,
            'commissions' => $commissions,
            'monthlyCommissions' => $monthlyCommissions,
            'dailyCommissions' => $dailyCommissions,
            'pagination' => $pagination,
            'type' => $type
        ]);
    }
    
    public function profile() {
        $userId = $_SESSION['user_id'];
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleProfileUpdate();
        }
        
        $user = $this->userModel->find($userId);
        
        $this->render('user/profile', [
            'title' => 'My Profile - ShaktiPure MLM',
            'user' => $user,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function team() {
        $userId = $_SESSION['user_id'];
        
        // Get direct referrals
        $directReferrals = $this->userModel->findBySponsorId($userId);
        
        // Get team statistics
        $teamStats = [];
        foreach ($directReferrals as $referral) {
            $referralStats = $this->userModel->getUserStats($referral['id']);
            $teamStats[$referral['id']] = $referralStats;
        }
        
        $this->render('user/team', [
            'title' => 'My Team - ShaktiPure MLM',
            'directReferrals' => $directReferrals,
            'teamStats' => $teamStats
        ]);
    }
    
    public function getTreeData() {
        $userId = $this->getGet('user_id', $_SESSION['user_id']);
        $levels = min(5, max(1, (int)$this->getGet('levels', 3)));
        
        $tree = $this->userModel->getGenealogy($userId, $levels);
        
        $this->renderJSON([
            'success' => true,
            'tree' => $tree
        ]);
    }
    
    public function details() {
        $userId = (int)$this->getPost('user_id');
        
        if (!$userId) {
            $this->renderJSON(['success' => false, 'message' => 'Invalid user ID']);
        }
        
        $user = $this->userModel->find($userId);
        $stats = $this->userModel->getUserStats($userId);
        
        if ($user) {
            $this->renderJSON([
                'success' => true,
                'user' => array_merge($user, $stats)
            ]);
        } else {
            $this->renderJSON(['success' => false, 'message' => 'User not found']);
        }
    }
    
    private function handleWithdrawRequest() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $userId = $_SESSION['user_id'];
        $amount = (float)$this->getPost('amount');
        $accountHolder = sanitizeInput($this->getPost('account_holder'));
        $accountNumber = sanitizeInput($this->getPost('account_number'));
        $bankName = sanitizeInput($this->getPost('bank_name'));
        $ifscCode = sanitizeInput($this->getPost('ifsc_code'));
        $branch = sanitizeInput($this->getPost('branch'));
        
        // Validate input
        $errors = $this->validate([
            'amount' => $amount,
            'account_holder' => $accountHolder,
            'account_number' => $accountNumber,
            'bank_name' => $bankName,
            'ifsc_code' => $ifscCode
        ], [
            'amount' => 'required|numeric',
            'account_holder' => 'required|min:2',
            'account_number' => 'required|min:8',
            'bank_name' => 'required|min:2',
            'ifsc_code' => 'required|min:11'
        ]);
        
        if (!empty($errors)) {
            $this->setFlash('error', 'Please fill all required fields correctly');
            return;
        }
        
        // Check withdrawal capability
        $withdrawalCheck = $this->withdrawalModel->canUserWithdraw($userId, $amount);
        
        if (!$withdrawalCheck['can_withdraw']) {
            $this->setFlash('error', 'Insufficient balance or amount below minimum withdrawal limit');
            return;
        }
        
        $bankDetails = [
            'account_holder' => $accountHolder,
            'account_number' => $accountNumber,
            'bank_name' => $bankName,
            'ifsc_code' => $ifscCode,
            'branch' => $branch
        ];
        
        try {
            $withdrawalId = $this->withdrawalModel->createRequest($userId, $amount, $bankDetails);
            
            if ($withdrawalId) {
                $this->setFlash('success', 'Withdrawal request submitted successfully. Request ID: #' . $withdrawalId);
                $this->logActivity('Withdrawal request', "Amount: ₹$amount");
            } else {
                $this->setFlash('error', 'Failed to submit withdrawal request');
            }
        } catch (Exception $e) {
            $this->setFlash('error', $e->getMessage());
        }
        
        $this->redirect('index.php?page=user&action=withdraw');
    }
    
    private function handleProfileUpdate() {
        if (!$this->validateCSRF()) {
            return;
        }
        
        $userId = $_SESSION['user_id'];
        $name = sanitizeInput($this->getPost('name'));
        $phone = sanitizeInput($this->getPost('phone'));
        $currentPassword = $this->getPost('current_password');
        $newPassword = $this->getPost('new_password');
        $confirmPassword = $this->getPost('confirm_password');
        
        $updateData = [
            'name' => $name,
            'phone' => $phone
        ];
        
        // Validate basic info
        $errors = $this->validate([
            'name' => $name,
            'phone' => $phone
        ], [
            'name' => 'required|min:2',
            'phone' => 'required|min:10'
        ]);
        
        // If password change is requested
        if ($newPassword) {
            if (!$currentPassword) {
                $errors['current_password'] = 'Current password is required';
            } else {
                // Verify current password
                $user = $this->userModel->find($userId);
                if (!password_verify($currentPassword, $user['password'])) {
                    $errors['current_password'] = 'Current password is incorrect';
                }
            }
            
            if ($newPassword !== $confirmPassword) {
                $errors['confirm_password'] = 'Passwords do not match';
            }
            
            if (strlen($newPassword) < 6) {
                $errors['new_password'] = 'Password must be at least 6 characters';
            }
            
            if (empty($errors)) {
                $updateData['password'] = password_hash($newPassword, PASSWORD_DEFAULT);
            }
        }
        
        if (empty($errors)) {
            if ($this->userModel->update($userId, $updateData)) {
                $_SESSION['user_name'] = $name; // Update session
                $this->setFlash('success', 'Profile updated successfully');
                $this->logActivity('Profile updated');
            } else {
                $this->setFlash('error', 'Failed to update profile');
            }
        } else {
            $this->setFlash('error', 'Please fix the errors and try again');
        }
        
        $this->redirect('index.php?page=user&action=profile');
    }
}
?>
