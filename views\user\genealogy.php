<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">
                <i class="fas fa-sitemap"></i> Binary Genealogy Tree
            </h1>
            <p class="text-muted">View your network structure</p>
        </div>
        <div class="col-md-6 text-md-end">
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" onclick="expandTree()">
                    <i class="fas fa-expand"></i> Expand
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="collapseTree()">
                    <i class="fas fa-compress"></i> Collapse
                </button>
                <button type="button" class="btn btn-outline-info" onclick="refreshTree()">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
        </div>
    </div>
    
    <!-- User Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="card-title mb-1">
                                <?php echo htmlspecialchars($user['name']); ?>
                                <?php if ($viewUserId != $_SESSION['user_id']): ?>
                                    <small class="text-muted">(Viewing)</small>
                                <?php endif; ?>
                            </h5>
                            <p class="card-text text-muted mb-2"><?php echo htmlspecialchars($user['email']); ?></p>
                            <div class="d-flex gap-3">
                                <small><strong>Left BV:</strong> <?php echo number_format($user['left_bv']); ?></small>
                                <small><strong>Right BV:</strong> <?php echo number_format($user['right_bv']); ?></small>
                                <small><strong>Total Pairs:</strong> <?php echo number_format($user['total_pairs']); ?></small>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <?php if ($viewUserId != $_SESSION['user_id']): ?>
                                <a href="<?php echo SITE_URL; ?>?page=user&action=genealogy" class="btn btn-primary">
                                    <i class="fas fa-home"></i> My Tree
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Binary Tree Container -->
    <div class="tree-container">
        <div id="binaryTree" class="tree">
            <!-- Tree will be rendered here -->
        </div>
    </div>
    
    <!-- Tree Legend -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Legend
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center mb-2">
                                <div class="tree-node active-user me-2" style="min-width: 80px; font-size: 10px;">Active User</div>
                                <span>Active member with purchases</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center mb-2">
                                <div class="tree-node me-2" style="min-width: 80px; font-size: 10px;">Regular User</div>
                                <span>Registered member</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center mb-2">
                                <div class="tree-node empty-slot me-2" style="min-width: 80px; font-size: 10px;">Empty Slot</div>
                                <span>Available position</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-info me-2">L</span>
                                <span>Left Position</span>
                                <span class="badge bg-success ms-3 me-2">R</span>
                                <span>Right Position</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user"></i> User Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- User details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Tree data from PHP
const treeData = <?php echo json_encode($tree); ?>;
const currentUserId = <?php echo $viewUserId; ?>;

// Render the binary tree
function renderBinaryTree(data, containerId) {
    const container = document.getElementById(containerId);
    container.innerHTML = '';
    
    if (!data || Object.keys(data).length === 0) {
        container.innerHTML = '<div class="text-center py-5"><i class="fas fa-sitemap text-muted fa-3x mb-3"></i><h5 class="text-muted">No tree data available</h5></div>';
        return;
    }
    
    // Find root user
    const rootUser = data[currentUserId];
    if (!rootUser) {
        container.innerHTML = '<div class="text-center py-5"><div class="alert alert-warning">User not found in tree data</div></div>';
        return;
    }
    
    // Create tree structure
    const treeHtml = buildTreeNode(rootUser, data, 0);
    container.innerHTML = treeHtml;
}

function buildTreeNode(user, allData, level) {
    if (level > 4) return ''; // Limit depth
    
    const hasChildren = user.children && user.children.length > 0;
    const leftChild = hasChildren ? allData[user.children.find(id => allData[id]?.position === 'left')] : null;
    const rightChild = hasChildren ? allData[user.children.find(id => allData[id]?.position === 'right')] : null;
    
    let html = '<ul>';
    html += '<li>';
    
    // Current user node
    html += buildUserNode(user);
    
    // Children
    if (hasChildren || level < 2) {
        html += '<ul>';
        
        // Left child
        html += '<li>';
        if (leftChild) {
            html += buildTreeNode(leftChild, allData, level + 1);
        } else {
            html += buildEmptyNode('left');
        }
        html += '</li>';
        
        // Right child
        html += '<li>';
        if (rightChild) {
            html += buildTreeNode(rightChild, allData, level + 1);
        } else {
            html += buildEmptyNode('right');
        }
        html += '</li>';
        
        html += '</ul>';
    }
    
    html += '</li>';
    html += '</ul>';
    
    return html;
}

function buildUserNode(user) {
    const isActive = user.total_pairs > 0 || user.left_bv > 0 || user.right_bv > 0;
    const nodeClass = isActive ? 'tree-node active-user' : 'tree-node';
    
    return `
        <div class="${nodeClass}" onclick="showUserDetails(${user.id})" data-user-id="${user.id}">
            <div class="fw-bold">${user.name}</div>
            <small>ID: ${user.id}</small>
            <div class="mt-1">
                <small class="badge bg-info">L: ${user.left_bv}</small>
                <small class="badge bg-success">R: ${user.right_bv}</small>
            </div>
            <small>Pairs: ${user.total_pairs}</small>
        </div>
    `;
}

function buildEmptyNode(position) {
    const positionClass = position === 'left' ? 'bg-info' : 'bg-success';
    return `
        <div class="tree-node empty-slot">
            <div class="text-muted">Empty Slot</div>
            <span class="badge ${positionClass}">${position.toUpperCase()}</span>
            <small class="text-muted">Available</small>
        </div>
    `;
}

function showUserDetails(userId) {
    const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
    modal.show();
    
    // Load user details
    fetch('<?php echo SITE_URL; ?>?page=user&action=details', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const user = data.user;
            document.getElementById('userDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Name:</strong> ${user.name}<br>
                        <strong>Email:</strong> ${user.email}<br>
                        <strong>Phone:</strong> ${user.phone || 'N/A'}<br>
                        <strong>Role:</strong> ${user.role}<br>
                        <strong>Status:</strong> <span class="badge bg-${user.status === 'active' ? 'success' : 'secondary'}">${user.status}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>Left BV:</strong> ${user.left_bv}<br>
                        <strong>Right BV:</strong> ${user.right_bv}<br>
                        <strong>Total Pairs:</strong> ${user.total_pairs}<br>
                        <strong>Wallet Balance:</strong> ₹${user.wallet_balance || 0}<br>
                        <strong>Direct Referrals:</strong> ${user.direct_referrals || 0}
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <a href="?page=user&action=genealogy&user_id=${user.id}" class="btn btn-primary btn-sm">
                        <i class="fas fa-sitemap"></i> View Tree
                    </a>
                </div>
            `;
        } else {
            document.getElementById('userDetailsContent').innerHTML = '<div class="alert alert-danger">Failed to load user details</div>';
        }
    })
    .catch(error => {
        document.getElementById('userDetailsContent').innerHTML = '<div class="alert alert-danger">Error loading user details</div>';
    });
}

function expandTree() {
    document.querySelectorAll('.tree ul').forEach(ul => {
        ul.style.display = 'block';
    });
}

function collapseTree() {
    document.querySelectorAll('.tree ul ul').forEach(ul => {
        ul.style.display = 'none';
    });
}

function refreshTree() {
    location.reload();
}

// Initialize tree on page load
document.addEventListener('DOMContentLoaded', function() {
    renderBinaryTree(treeData, 'binaryTree');
});
</script>

<style>
/* Additional tree styles for better visualization */
.tree ul {
    padding-top: 20px;
    position: relative;
    transition: all 0.5s;
}

.tree li {
    float: left;
    text-align: center;
    list-style-type: none;
    position: relative;
    padding: 20px 5px 0 5px;
    transition: all 0.5s;
}

.tree li::before, .tree li::after {
    content: '';
    position: absolute;
    top: 0;
    right: 50%;
    border-top: 2px solid #ccc;
    width: 50%;
    height: 20px;
}

.tree li::after {
    right: auto;
    left: 50%;
    border-left: 2px solid #ccc;
}

.tree li:only-child::after, .tree li:only-child::before {
    display: none;
}

.tree li:only-child {
    padding-top: 0;
}

.tree li:first-child::before, .tree li:last-child::after {
    border: 0 none;
}

.tree li:last-child::before {
    border-right: 2px solid #ccc;
    border-radius: 0 5px 0 0;
}

.tree li:first-child::after {
    border-radius: 5px 0 0 0;
}

.tree ul ul::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    border-left: 2px solid #ccc;
    width: 0;
    height: 20px;
}

.tree-node {
    border: 2px solid #007bff;
    padding: 10px;
    text-decoration: none;
    color: #333;
    font-family: arial, verdana, tahoma;
    font-size: 11px;
    display: inline-block;
    border-radius: 10px;
    transition: all 0.5s;
    background: white;
    min-width: 120px;
    cursor: pointer;
}

.tree-node:hover {
    background: #007bff;
    color: white;
    border: 2px solid #0056b3;
    transform: scale(1.05);
}

.tree-node.active-user {
    background: #28a745;
    color: white;
    border-color: #1e7e34;
}

.tree-node.empty-slot {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    color: #6c757d;
    cursor: default;
}

.tree-node.empty-slot:hover {
    background: #f8f9fa;
    color: #6c757d;
    transform: none;
}
</style>
