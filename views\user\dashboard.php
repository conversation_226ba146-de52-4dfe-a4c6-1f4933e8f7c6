<div class="container-fluid">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt"></i> Welcome, <?php echo htmlspecialchars($user['name']); ?>!
            </h1>
            <p class="text-muted">Your MLM Dashboard - Track your success journey</p>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card dashboard-card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo formatCurrency($walletSummary['balance']); ?></h4>
                            <p class="card-text">Wallet Balance</p>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                    </div>
                    <small class="text-white-50">
                        <i class="fas fa-arrow-up"></i> Available for withdrawal
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card dashboard-card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo formatCurrency($walletSummary['total_earnings']); ?></h4>
                            <p class="card-text">Total Earnings</p>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                    <small class="text-white-50">
                        <i class="fas fa-chart-line"></i> Lifetime earnings
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card dashboard-card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo number_format($user['total_pairs']); ?></h4>
                            <p class="card-text">Total Pairs</p>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-handshake"></i>
                        </div>
                    </div>
                    <small class="text-white-50">
                        <i class="fas fa-sitemap"></i> Binary pairs formed
                    </small>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card dashboard-card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title"><?php echo number_format($stats['direct_referrals']); ?></h4>
                            <p class="card-text">Direct Referrals</p>
                        </div>
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <small class="text-white-50">
                        <i class="fas fa-user-plus"></i> Your team members
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- BV and Team Stats -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-info"><?php echo number_format($user['left_bv']); ?></h5>
                    <small class="text-muted">Left BV</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-success"><?php echo number_format($user['right_bv']); ?></h5>
                    <small class="text-muted">Right BV</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-primary"><?php echo number_format($stats['total_downline']); ?></h5>
                    <small class="text-muted">Total Downline</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-warning"><?php echo number_format($stats['total_purchases']); ?></h5>
                    <small class="text-muted">Total Purchases</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt text-primary"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=product&action=catalog" class="btn btn-outline-primary w-100">
                                <i class="fas fa-shopping-cart"></i> Buy Products
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=user&action=genealogy" class="btn btn-outline-success w-100">
                                <i class="fas fa-sitemap"></i> View Tree
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=user&action=withdraw" class="btn btn-outline-warning w-100">
                                <i class="fas fa-money-bill-wave"></i> Withdraw
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=user&action=team" class="btn btn-outline-info w-100">
                                <i class="fas fa-users"></i> My Team
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activities -->
    <div class="row g-4">
        <!-- Recent Purchases -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shopping-cart text-primary"></i> Recent Purchases
                    </h5>
                    <a href="<?php echo SITE_URL; ?>?page=product&action=catalog" class="btn btn-sm btn-outline-primary">
                        Buy More
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($recentPurchases)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recentPurchases as $purchase): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($purchase['product_name']); ?></h6>
                                            <small class="text-muted"><?php echo formatDate($purchase['created_at']); ?></small>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold text-success"><?php echo formatCurrency($purchase['amount']); ?></div>
                                            <small class="text-muted">BV: <?php echo $purchase['bv']; ?></small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart text-muted fa-2x mb-2"></i>
                            <p class="text-muted">No purchases yet</p>
                            <a href="<?php echo SITE_URL; ?>?page=product&action=catalog" class="btn btn-sm btn-primary">
                                Start Shopping
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Commissions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-coins text-success"></i> Recent Commissions
                    </h5>
                    <a href="<?php echo SITE_URL; ?>?page=user&action=commissions" class="btn btn-sm btn-outline-success">
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($recentCommissions)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recentCommissions as $commission): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <span class="badge bg-<?php 
                                                    echo $commission['type'] === 'pairing' ? 'primary' : 
                                                        ($commission['type'] === 'referral' ? 'info' : 'warning'); 
                                                ?>">
                                                    <?php echo ucfirst($commission['type']); ?>
                                                </span>
                                            </h6>
                                            <p class="mb-1 text-muted small">from <?php echo htmlspecialchars($commission['from_user_name']); ?></p>
                                            <small class="text-muted"><?php echo formatDate($commission['created_at']); ?></small>
                                        </div>
                                        <div class="fw-bold text-success">
                                            <?php echo formatCurrency($commission['amount']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-coins text-muted fa-2x mb-2"></i>
                            <p class="text-muted">No commissions yet</p>
                            <small class="text-muted">Build your team to earn commissions</small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Transactions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exchange-alt text-info"></i> Recent Transactions
                    </h5>
                    <a href="<?php echo SITE_URL; ?>?page=user&action=wallet" class="btn btn-sm btn-outline-info">
                        View Wallet
                    </a>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($recentTransactions)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recentTransactions as $transaction): ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-<?php 
                                                    echo $transaction['type'] === 'pairing' ? 'handshake' : 
                                                        ($transaction['type'] === 'withdrawal' ? 'money-bill-wave' : 'gift'); 
                                                ?>"></i>
                                                <?php echo ucfirst($transaction['type']); ?>
                                            </h6>
                                            <p class="mb-1 text-muted small"><?php echo htmlspecialchars($transaction['description']); ?></p>
                                            <small class="text-muted"><?php echo formatDate($transaction['created_at']); ?></small>
                                        </div>
                                        <div class="fw-bold <?php echo in_array($transaction['type'], ['withdrawal', 'debit']) ? 'text-danger' : 'text-success'; ?>">
                                            <?php echo in_array($transaction['type'], ['withdrawal', 'debit']) ? '-' : '+'; ?>
                                            <?php echo formatCurrency($transaction['amount']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-exchange-alt text-muted fa-2x mb-2"></i>
                            <p class="text-muted">No transactions yet</p>
                            <small class="text-muted">Your transactions will appear here</small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Referral Link -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-share-alt text-primary"></i> Your Referral Link
                    </h5>
                    <p class="card-text">Share this link to invite new members to your team:</p>
                    <div class="input-group">
                        <input type="text" class="form-control" id="referralLink" 
                               value="<?php echo SITE_URL; ?>?page=auth&action=register&sponsor_id=<?php echo $user['id']; ?>" readonly>
                        <button class="btn btn-outline-primary copy-btn" type="button" data-target="#referralLink">
                            <i class="fas fa-copy"></i> Copy
                        </button>
                    </div>
                    <small class="text-muted">Your ID: <?php echo $user['id']; ?></small>
                </div>
            </div>
        </div>
    </div>
</div>
