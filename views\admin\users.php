<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">
                <i class="fas fa-users"></i> User Management
            </h1>
            <p class="text-muted">Manage all system users</p>
        </div>
        <div class="col-md-6">
            <!-- Search and Filter -->
            <form method="GET" class="d-flex gap-2">
                <input type="hidden" name="page" value="admin">
                <input type="hidden" name="action" value="users">
                
                <select name="role" class="form-select" onchange="this.form.submit()">
                    <option value="">All Roles</option>
                    <option value="user" <?php echo $role === 'user' ? 'selected' : ''; ?>>Users</option>
                    <option value="franchise" <?php echo $role === 'franchise' ? 'selected' : ''; ?>>Franchises</option>
                    <option value="admin" <?php echo $role === 'admin' ? 'selected' : ''; ?>>Admins</option>
                </select>
                
                <div class="input-group">
                    <input type="text" name="search" class="form-control" placeholder="Search users..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-secondary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Users Table -->
    <div class="card table-custom">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> All Users
            </h5>
            <span class="badge bg-primary"><?php echo number_format($pagination['total_items']); ?> Total</span>
        </div>
        <div class="card-body p-0">
            <?php if (!empty($users)): ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Role</th>
                                <th>Sponsor</th>
                                <th>Position</th>
                                <th>BV</th>
                                <th>Wallet</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-0"><?php echo htmlspecialchars($user['name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                            <?php if ($user['phone']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($user['phone']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $user['role'] === 'admin' ? 'danger' : 
                                                ($user['role'] === 'franchise' ? 'warning' : 'primary'); 
                                        ?>">
                                            <?php echo ucfirst($user['role']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($user['sponsor_id']): ?>
                                            <small class="text-muted">ID: <?php echo $user['sponsor_id']; ?></small>
                                        <?php else: ?>
                                            <small class="text-muted">-</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['position']): ?>
                                            <span class="badge bg-<?php echo $user['position'] === 'left' ? 'info' : 'success'; ?>">
                                                <?php echo ucfirst($user['position']); ?>
                                            </span>
                                        <?php else: ?>
                                            <small class="text-muted">-</small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small>
                                            L: <?php echo number_format($user['left_bv'], 0); ?><br>
                                            R: <?php echo number_format($user['right_bv'], 0); ?>
                                        </small>
                                    </td>
                                    <td class="text-success fw-bold">
                                        <?php echo formatCurrency($user['stats']['wallet_balance'] ?? 0); ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($user['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo formatDate($user['created_at']); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-info" 
                                                    onclick="viewUserDetails(<?php echo $user['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-primary" 
                                                    onclick="viewGenealogy(<?php echo $user['id']; ?>)">
                                                <i class="fas fa-sitemap"></i>
                                            </button>
                                            <?php if ($user['status'] === 'active'): ?>
                                                <button type="button" class="btn btn-outline-warning" 
                                                        onclick="toggleUserStatus(<?php echo $user['id']; ?>, 'inactive')">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-outline-success" 
                                                        onclick="toggleUserStatus(<?php echo $user['id']; ?>, 'active')">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="card-footer">
                        <nav aria-label="Users pagination">
                            <ul class="pagination pagination-sm mb-0 justify-content-center">
                                <?php if ($pagination['has_prev']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=admin&action=users&page=<?php echo $pagination['prev_page']; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $pagination['current_page'] ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=admin&action=users&page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['has_next']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=admin&action=users&page=<?php echo $pagination['next_page']; ?>&search=<?php echo urlencode($search); ?>&role=<?php echo urlencode($role); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                Showing <?php echo (($pagination['current_page'] - 1) * $pagination['items_per_page']) + 1; ?> 
                                to <?php echo min($pagination['current_page'] * $pagination['items_per_page'], $pagination['total_items']); ?> 
                                of <?php echo number_format($pagination['total_items']); ?> users
                            </small>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted fa-3x mb-3"></i>
                    <h5 class="text-muted">No users found</h5>
                    <?php if ($search): ?>
                        <p class="text-muted">No users match your search criteria</p>
                        <a href="?page=admin&action=users" class="btn btn-outline-primary">
                            <i class="fas fa-times"></i> Clear Search
                        </a>
                    <?php else: ?>
                        <p class="text-muted">Users will appear here once they register</p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user"></i> User Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <div class="text-center">
                    <div class="spinner"></div>
                    <p>Loading user details...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Genealogy Modal -->
<div class="modal fade" id="genealogyModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-sitemap"></i> User Genealogy
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="genealogyContent">
                <div class="text-center">
                    <div class="spinner"></div>
                    <p>Loading genealogy tree...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewUserDetails(userId) {
    const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
    modal.show();
    
    // Load user details via AJAX (you would implement this endpoint)
    fetch(`?page=admin&action=getUserDetails&user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('userDetailsContent').innerHTML = data.html;
            } else {
                document.getElementById('userDetailsContent').innerHTML = '<div class="alert alert-danger">Failed to load user details</div>';
            }
        })
        .catch(error => {
            document.getElementById('userDetailsContent').innerHTML = '<div class="alert alert-danger">Error loading user details</div>';
        });
}

function viewGenealogy(userId) {
    const modal = new bootstrap.Modal(document.getElementById('genealogyModal'));
    modal.show();
    
    // Load genealogy tree via AJAX (you would implement this endpoint)
    fetch(`?page=admin&action=getGenealogy&user_id=${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('genealogyContent').innerHTML = data.html;
            } else {
                document.getElementById('genealogyContent').innerHTML = '<div class="alert alert-danger">Failed to load genealogy tree</div>';
            }
        })
        .catch(error => {
            document.getElementById('genealogyContent').innerHTML = '<div class="alert alert-danger">Error loading genealogy tree</div>';
        });
}

function toggleUserStatus(userId, newStatus) {
    if (confirm(`Are you sure you want to ${newStatus === 'active' ? 'activate' : 'deactivate'} this user?`)) {
        // Submit form to toggle user status (you would implement this endpoint)
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token ?? ''; ?>">
            <input type="hidden" name="action" value="toggle_user_status">
            <input type="hidden" name="user_id" value="${userId}">
            <input type="hidden" name="status" value="${newStatus}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
