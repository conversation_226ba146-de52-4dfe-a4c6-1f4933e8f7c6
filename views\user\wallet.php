<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">
                <i class="fas fa-wallet"></i> My Wallet
            </h1>
            <p class="text-muted">Track your earnings and transactions</p>
        </div>
        <div class="col-md-6 text-md-end">
            <a href="<?php echo SITE_URL; ?>?page=user&action=withdraw" class="btn btn-primary">
                <i class="fas fa-money-bill-wave"></i> Withdraw Funds
            </a>
        </div>
    </div>
    
    <!-- Wallet Balance Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="wallet-balance">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="balance-amount"><?php echo formatCurrency($walletSummary['balance']); ?></h2>
                        <p class="mb-0">Available Balance</p>
                        <small class="text-white-50">
                            Available for withdrawal: <?php echo formatCurrency($walletSummary['available_withdrawal']); ?>
                        </small>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <div class="d-flex flex-column gap-2">
                            <div>
                                <small class="text-white-50">Total Earnings</small>
                                <div class="fw-bold"><?php echo formatCurrency($walletSummary['total_earnings']); ?></div>
                            </div>
                            <div>
                                <small class="text-white-50">Total Withdrawals</small>
                                <div class="fw-bold"><?php echo formatCurrency($walletSummary['total_withdrawals']); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Earnings Breakdown -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-primary"><?php echo formatCurrency($walletSummary['total_pairing']); ?></h5>
                    <small class="text-muted">Pairing Income</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-success"><?php echo formatCurrency($walletSummary['total_referral']); ?></h5>
                    <small class="text-muted">Referral Bonus</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-info"><?php echo formatCurrency($walletSummary['total_bonus']); ?></h5>
                    <small class="text-muted">Other Bonus</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-warning"><?php echo formatCurrency($walletSummary['pending_withdrawals']); ?></h5>
                    <small class="text-muted">Pending Withdrawals</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Transaction History -->
    <div class="card table-custom">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-history"></i> Transaction History
            </h5>
            <div class="d-flex gap-2">
                <!-- Filter by type -->
                <form method="GET" class="d-flex">
                    <input type="hidden" name="page" value="user">
                    <input type="hidden" name="action" value="wallet">
                    <select name="type" class="form-select form-select-sm" onchange="this.form.submit()">
                        <option value="">All Types</option>
                        <option value="pairing" <?php echo $type === 'pairing' ? 'selected' : ''; ?>>Pairing</option>
                        <option value="referral" <?php echo $type === 'referral' ? 'selected' : ''; ?>>Referral</option>
                        <option value="bonus" <?php echo $type === 'bonus' ? 'selected' : ''; ?>>Bonus</option>
                        <option value="withdrawal" <?php echo $type === 'withdrawal' ? 'selected' : ''; ?>>Withdrawal</option>
                    </select>
                </form>
            </div>
        </div>
        <div class="card-body p-0">
            <?php if (!empty($transactions)): ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transactions as $transaction): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $transaction['type'] === 'pairing' ? 'primary' : 
                                                ($transaction['type'] === 'referral' ? 'info' : 
                                                ($transaction['type'] === 'bonus' ? 'success' : 'warning')); 
                                        ?>">
                                            <i class="fas fa-<?php 
                                                echo $transaction['type'] === 'pairing' ? 'handshake' : 
                                                    ($transaction['type'] === 'referral' ? 'users' : 
                                                    ($transaction['type'] === 'bonus' ? 'gift' : 'money-bill-wave')); 
                                            ?>"></i>
                                            <?php echo ucfirst($transaction['type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <?php echo htmlspecialchars($transaction['description']); ?>
                                            <?php if ($transaction['reference_id']): ?>
                                                <br><small class="text-muted">Ref: #<?php echo $transaction['reference_id']; ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="fw-bold <?php echo in_array($transaction['type'], ['withdrawal', 'debit']) ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo in_array($transaction['type'], ['withdrawal', 'debit']) ? '-' : '+'; ?>
                                        <?php echo formatCurrency($transaction['amount']); ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $transaction['status'] === 'completed' ? 'success' : 'warning'; ?>">
                                            <?php echo ucfirst($transaction['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo formatDate($transaction['created_at']); ?></small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="card-footer">
                        <nav aria-label="Transaction pagination">
                            <ul class="pagination pagination-sm mb-0 justify-content-center">
                                <?php if ($pagination['has_prev']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=user&action=wallet&page=<?php echo $pagination['prev_page']; ?>&type=<?php echo urlencode($type); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $pagination['current_page'] ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=user&action=wallet&page=<?php echo $i; ?>&type=<?php echo urlencode($type); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['has_next']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=user&action=wallet&page=<?php echo $pagination['next_page']; ?>&type=<?php echo urlencode($type); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-history text-muted fa-3x mb-3"></i>
                    <h5 class="text-muted">No transactions found</h5>
                    <?php if ($type): ?>
                        <p class="text-muted">No transactions of type: <?php echo ucfirst($type); ?></p>
                        <a href="?page=user&action=wallet" class="btn btn-outline-primary">
                            <i class="fas fa-times"></i> Clear Filter
                        </a>
                    <?php else: ?>
                        <p class="text-muted">Your transactions will appear here</p>
                        <a href="<?php echo SITE_URL; ?>?page=product&action=catalog" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> Start Shopping
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- Monthly Earnings Chart -->
    <?php if (!empty($monthlyEarnings)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar"></i> Monthly Earnings Breakdown
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($monthlyEarnings as $earning): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="text-center">
                                        <h6 class="text-<?php 
                                            echo $earning['type'] === 'pairing' ? 'primary' : 
                                                ($earning['type'] === 'referral' ? 'info' : 'success'); 
                                        ?>">
                                            <?php echo formatCurrency($earning['total']); ?>
                                        </h6>
                                        <small class="text-muted">
                                            <?php echo ucfirst($earning['type']); ?> (<?php echo $earning['count']; ?> transactions)
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=user&action=withdraw" class="btn btn-outline-primary w-100">
                                <i class="fas fa-money-bill-wave"></i> Withdraw Funds
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=user&action=commissions" class="btn btn-outline-success w-100">
                                <i class="fas fa-coins"></i> View Commissions
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=product&action=catalog" class="btn btn-outline-info w-100">
                                <i class="fas fa-shopping-cart"></i> Buy Products
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?php echo SITE_URL; ?>?page=user&action=team" class="btn btn-outline-warning w-100">
                                <i class="fas fa-users"></i> Build Team
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
