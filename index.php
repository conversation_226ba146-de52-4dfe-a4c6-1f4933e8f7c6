<?php
/**
 * MLM Binary Plan System
 * Main Entry Point
 */

// Start session
session_start();

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define constants
define('ROOT_PATH', __DIR__);
define('CONFIG_PATH', ROOT_PATH . '/config');
define('CONTROLLERS_PATH', ROOT_PATH . '/controllers');
define('MODELS_PATH', ROOT_PATH . '/models');
define('VIEWS_PATH', ROOT_PATH . '/views');
define('ASSETS_PATH', ROOT_PATH . '/assets');

// Include configuration
require_once CONFIG_PATH . '/config.php';
require_once CONFIG_PATH . '/database.php';

// Simple routing
$page = $_GET['page'] ?? 'home';
$action = $_GET['action'] ?? 'index';

// Route to appropriate controller
switch($page) {
    case 'auth':
        require_once CONTROLLERS_PATH . '/AuthController.php';
        $controller = new AuthController();
        break;
    
    case 'user':
        require_once CONTROLLERS_PATH . '/UserController.php';
        $controller = new UserController();
        break;
    
    case 'franchise':
        require_once CONTROLLERS_PATH . '/FranchiseController.php';
        $controller = new FranchiseController();
        break;
    
    case 'admin':
        require_once CONTROLLERS_PATH . '/AdminController.php';
        $controller = new AdminController();
        break;
    
    case 'product':
        require_once CONTROLLERS_PATH . '/ProductController.php';
        $controller = new ProductController();
        break;
    
    case 'payment':
        require_once CONTROLLERS_PATH . '/PaymentController.php';
        $controller = new PaymentController();
        break;
    
    default:
        require_once CONTROLLERS_PATH . '/HomeController.php';
        $controller = new HomeController();
        break;
}

// Execute action
if (method_exists($controller, $action)) {
    $controller->$action();
} else {
    $controller->index();
}
?>
