<?php
require_once CONTROLLERS_PATH . '/BaseController.php';
require_once MODELS_PATH . '/Product.php';
require_once MODELS_PATH . '/UserProduct.php';
require_once MODELS_PATH . '/User.php';

class ProductController extends BaseController {
    private $productModel;
    private $userProductModel;
    private $userModel;
    
    public function __construct() {
        parent::__construct();
        $this->productModel = new Product();
        $this->userProductModel = new UserProduct();
        $this->userModel = new User();
    }
    
    public function catalog() {
        $page = max(1, (int)$this->getGet('page', 1));
        $search = $this->getGet('search', '');
        $priceMin = (float)$this->getGet('price_min', 0);
        $priceMax = (float)$this->getGet('price_max', 0);
        $limit = 12;
        
        if ($search) {
            $products = $this->productModel->searchProducts($search);
            $totalProducts = count($products);
        } elseif ($priceMin || $priceMax) {
            $priceMax = $priceMax ?: 999999;
            $products = $this->productModel->getProductsByPriceRange($priceMin, $priceMax);
            $totalProducts = count($products);
        } else {
            $products = $this->productModel->getCatalogProducts($page, $limit);
            $totalProducts = $this->productModel->getTotalProductsCount();
        }
        
        $pagination = $this->paginate($totalProducts, $limit, $page);
        
        $this->render('product/catalog', [
            'title' => 'Product Catalog - ShaktiPure MLM',
            'products' => $products,
            'pagination' => $pagination,
            'search' => $search,
            'priceMin' => $priceMin,
            'priceMax' => $priceMax
        ]);
    }
    
    public function details() {
        $productId = (int)$this->getGet('id');
        
        if (!$productId) {
            $this->setFlash('error', 'Product not found');
            $this->redirect('index.php?page=product&action=catalog');
        }
        
        $product = $this->productModel->getProductWithImage($productId);
        
        if (!$product || $product['status'] !== 'active') {
            $this->setFlash('error', 'Product not found or unavailable');
            $this->redirect('index.php?page=product&action=catalog');
        }
        
        // Get product statistics
        $stats = $this->productModel->getProductStats($productId);
        
        // Get related products
        $relatedProducts = $this->productModel->getActiveProducts();
        $relatedProducts = array_filter($relatedProducts, function($p) use ($productId) {
            return $p['id'] != $productId;
        });
        $relatedProducts = array_slice($relatedProducts, 0, 4);
        
        $this->render('product/details', [
            'title' => $product['name'] . ' - ShaktiPure MLM',
            'product' => $product,
            'stats' => $stats,
            'relatedProducts' => $relatedProducts
        ]);
    }
    
    public function addToCart() {
        $this->requireLogin();
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('index.php?page=product&action=catalog');
        }
        
        $productId = (int)$this->getPost('product_id');
        $quantity = max(1, (int)$this->getPost('quantity', 1));
        
        $product = $this->productModel->find($productId);
        
        if (!$product || $product['status'] !== 'active') {
            $this->setFlash('error', 'Product not available');
            $this->redirect('index.php?page=product&action=catalog');
        }
        
        // Initialize cart in session
        if (!isset($_SESSION['cart'])) {
            $_SESSION['cart'] = [];
        }
        
        // Add or update product in cart
        if (isset($_SESSION['cart'][$productId])) {
            $_SESSION['cart'][$productId]['quantity'] += $quantity;
        } else {
            $_SESSION['cart'][$productId] = [
                'product_id' => $productId,
                'name' => $product['name'],
                'price' => $product['price'],
                'bv' => $product['bv'],
                'image' => $product['image'],
                'quantity' => $quantity
            ];
        }
        
        $this->setFlash('success', 'Product added to cart successfully');
        $this->redirect('index.php?page=product&action=cart');
    }
    
    public function cart() {
        $this->requireLogin();
        
        $cart = $_SESSION['cart'] ?? [];
        $cartTotal = 0;
        $totalBV = 0;
        
        foreach ($cart as &$item) {
            $item['total'] = $item['price'] * $item['quantity'];
            $item['total_bv'] = $item['bv'] * $item['quantity'];
            $cartTotal += $item['total'];
            $totalBV += $item['total_bv'];
            
            // Add image URL
            if ($item['image']) {
                $item['image_url'] = SITE_URL . '/uploads/' . $item['image'];
            } else {
                $item['image_url'] = SITE_URL . '/assets/images/no-image.png';
            }
        }
        
        $this->render('product/cart', [
            'title' => 'Shopping Cart - ShaktiPure MLM',
            'cart' => $cart,
            'cartTotal' => $cartTotal,
            'totalBV' => $totalBV,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function updateCart() {
        $this->requireLogin();
        
        if (!$this->validateCSRF()) {
            $this->redirect('index.php?page=product&action=cart');
        }
        
        $action = $this->getPost('action');
        $productId = (int)$this->getPost('product_id');
        
        if (!isset($_SESSION['cart'][$productId])) {
            $this->setFlash('error', 'Product not found in cart');
            $this->redirect('index.php?page=product&action=cart');
        }
        
        switch ($action) {
            case 'update':
                $quantity = max(1, (int)$this->getPost('quantity', 1));
                $_SESSION['cart'][$productId]['quantity'] = $quantity;
                $this->setFlash('success', 'Cart updated successfully');
                break;
                
            case 'remove':
                unset($_SESSION['cart'][$productId]);
                $this->setFlash('success', 'Product removed from cart');
                break;
        }
        
        $this->redirect('index.php?page=product&action=cart');
    }
    
    public function checkout() {
        $this->requireLogin();
        
        $cart = $_SESSION['cart'] ?? [];
        
        if (empty($cart)) {
            $this->setFlash('error', 'Your cart is empty');
            $this->redirect('index.php?page=product&action=catalog');
        }
        
        $cartTotal = 0;
        $totalBV = 0;
        
        foreach ($cart as $item) {
            $cartTotal += $item['price'] * $item['quantity'];
            $totalBV += $item['bv'] * $item['quantity'];
        }
        
        // Get user details for Razorpay
        $user = $this->userModel->find($_SESSION['user_id']);
        
        $this->render('product/checkout', [
            'title' => 'Checkout - ShaktiPure MLM',
            'cart' => $cart,
            'cartTotal' => $cartTotal,
            'totalBV' => $totalBV,
            'user' => $user,
            'razorpayKeyId' => RAZORPAY_KEY_ID,
            'csrf_token' => $this->generateCSRF()
        ]);
    }
    
    public function processOrder() {
        $this->requireLogin();
        
        if (!$this->validateCSRF()) {
            $this->renderJSON(['success' => false, 'message' => 'Invalid request']);
        }
        
        $cart = $_SESSION['cart'] ?? [];
        
        if (empty($cart)) {
            $this->renderJSON(['success' => false, 'message' => 'Cart is empty']);
        }
        
        $userId = $_SESSION['user_id'];
        $cartTotal = 0;
        $totalBV = 0;
        
        // Calculate totals
        foreach ($cart as $item) {
            $cartTotal += $item['price'] * $item['quantity'];
            $totalBV += $item['bv'] * $item['quantity'];
        }
        
        try {
            // Create Razorpay order
            $orderId = $this->createRazorpayOrder($cartTotal);
            
            if ($orderId) {
                // Store order details in session for verification
                $_SESSION['pending_order'] = [
                    'order_id' => $orderId,
                    'amount' => $cartTotal,
                    'total_bv' => $totalBV,
                    'cart' => $cart,
                    'user_id' => $userId
                ];
                
                $this->renderJSON([
                    'success' => true,
                    'order_id' => $orderId,
                    'amount' => $cartTotal * 100, // Amount in paise
                    'currency' => 'INR'
                ]);
            } else {
                $this->renderJSON(['success' => false, 'message' => 'Failed to create order']);
            }
        } catch (Exception $e) {
            $this->renderJSON(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    private function createRazorpayOrder($amount) {
        // In a real implementation, you would use Razorpay SDK
        // For now, we'll generate a mock order ID
        return 'order_' . uniqid() . time();
    }
    
    public function clearCart() {
        $this->requireLogin();
        
        if (!$this->validateCSRF()) {
            $this->redirect('index.php?page=product&action=cart');
        }
        
        unset($_SESSION['cart']);
        $this->setFlash('success', 'Cart cleared successfully');
        $this->redirect('index.php?page=product&action=catalog');
    }
    
    public function getCartCount() {
        $cart = $_SESSION['cart'] ?? [];
        $count = 0;
        
        foreach ($cart as $item) {
            $count += $item['quantity'];
        }
        
        $this->renderJSON(['count' => $count]);
    }
}
?>
