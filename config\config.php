<?php
/**
 * Main Configuration File
 */

// Site Configuration
define('SITE_NAME', 'ShaktiPure MLM');
define('SITE_URL', 'http://localhost/shaktipure-mlm');
define('ADMIN_EMAIL', '<EMAIL>');

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'shaktipure_mlm');
define('DB_USER', 'root');
define('DB_PASS', '');

// Razorpay Configuration
define('RAZORPAY_KEY_ID', 'rzp_test_VlmnmcVPhKby27');
define('RAZORPAY_KEY_SECRET', 'L4XLmdPsKd6alBe315rbXsme');

// MLM Configuration
define('PAIRING_AMOUNT', 100); // Amount earned per pair
define('MIN_WITHDRAWAL', 500); // Minimum withdrawal amount
define('ADMIN_COMMISSION', 10); // Admin commission percentage

// Security Configuration
define('ENCRYPTION_KEY', 'shaktipuremlm');
define('SESSION_TIMEOUT', 3600); // 1 hour

// File Upload Configuration
define('MAX_FILE_SIZE', 2097152); // 2MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'gif']);

// Email Configuration (for future use)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_app_password');

// Timezone
date_default_timezone_set('Asia/Kolkata');

// Helper Functions
function redirect($url) {
    header("Location: " . SITE_URL . "/" . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getUserRole() {
    return $_SESSION['user_role'] ?? null;
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirect('index.php?page=auth&action=login');
    }
}

function requireRole($role) {
    requireLogin();
    if (getUserRole() !== $role) {
        redirect('index.php?page=auth&action=unauthorized');
    }
}

function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function generateToken() {
    return bin2hex(random_bytes(32));
}

function formatCurrency($amount) {
    return '₹' . number_format($amount, 2);
}

function formatDate($date) {
    return date('d M Y, h:i A', strtotime($date));
}
?>
