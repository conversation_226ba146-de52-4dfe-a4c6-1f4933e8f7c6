<?php
require_once CONFIG_PATH . '/database.php';

class Commission extends BaseModel {
    protected $table = 'commissions';
    
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * Record a commission
     */
    public function recordCommission($userId, $fromUserId, $type, $amount, $description, $referenceId = null) {
        $data = [
            'user_id' => $userId,
            'from_user_id' => $fromUserId,
            'type' => $type,
            'amount' => $amount,
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // Add BV data for pairing commissions
        if ($type === 'pairing') {
            require_once MODELS_PATH . '/User.php';
            $userModel = new User();
            $user = $userModel->find($userId);
            
            if ($user) {
                $data['left_bv'] = $user['left_bv'];
                $data['right_bv'] = $user['right_bv'];
            }
        }
        
        $commissionId = $this->create($data);
        
        if ($commissionId) {
            // Add to wallet
            require_once MODELS_PATH . '/Wallet.php';
            $wallet = new Wallet();
            $wallet->addTransaction($userId, $amount, $type, $description, $referenceId);
        }
        
        return $commissionId;
    }
    
    /**
     * Get user's commission history
     */
    public function getUserCommissions($userId, $limit = 50) {
        $stmt = $this->db->prepare("
            SELECT c.*, u.name as from_user_name, u.email as from_user_email
            FROM commissions c
            INNER JOIN users u ON c.from_user_id = u.id
            WHERE c.user_id = ?
            ORDER BY c.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get commissions by type
     */
    public function getCommissionsByType($userId, $type) {
        return $this->findAll([
            'user_id' => $userId,
            'type' => $type
        ], 'created_at DESC');
    }
    
    /**
     * Get total commissions by type
     */
    public function getTotalCommissionsByType($userId, $type) {
        $stmt = $this->db->prepare("
            SELECT SUM(amount) as total
            FROM commissions 
            WHERE user_id = ? AND type = ?
        ");
        $stmt->execute([$userId, $type]);
        $result = $stmt->fetch();
        
        return $result['total'] ?? 0;
    }
    
    /**
     * Get commission statistics
     */
    public function getCommissionStats($userId) {
        $stats = [];
        
        // Total by type
        $stats['pairing'] = $this->getTotalCommissionsByType($userId, 'pairing');
        $stats['referral'] = $this->getTotalCommissionsByType($userId, 'referral');
        $stats['bonus'] = $this->getTotalCommissionsByType($userId, 'bonus');
        $stats['total'] = $stats['pairing'] + $stats['referral'] + $stats['bonus'];
        
        // Count by type
        $stats['pairing_count'] = $this->count(['user_id' => $userId, 'type' => 'pairing']);
        $stats['referral_count'] = $this->count(['user_id' => $userId, 'type' => 'referral']);
        $stats['bonus_count'] = $this->count(['user_id' => $userId, 'type' => 'bonus']);
        
        return $stats;
    }
    
    /**
     * Get monthly commission report
     */
    public function getMonthlyCommissions($userId, $year = null, $month = null) {
        if (!$year) $year = date('Y');
        if (!$month) $month = date('m');
        
        $stmt = $this->db->prepare("
            SELECT 
                type,
                SUM(amount) as total,
                COUNT(*) as count,
                AVG(amount) as average
            FROM commissions 
            WHERE user_id = ? 
                AND YEAR(created_at) = ? 
                AND MONTH(created_at) = ?
            GROUP BY type
        ");
        $stmt->execute([$userId, $year, $month]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get daily commissions for chart
     */
    public function getDailyCommissions($userId, $days = 30) {
        $stmt = $this->db->prepare("
            SELECT 
                DATE(created_at) as date,
                type,
                SUM(amount) as total
            FROM commissions 
            WHERE user_id = ? 
                AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY DATE(created_at), type
            ORDER BY date ASC
        ");
        $stmt->execute([$userId, $days]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get top earners by commission type
     */
    public function getTopEarners($type = null, $limit = 10) {
        $sql = "
            SELECT 
                u.id,
                u.name,
                u.email,
                SUM(c.amount) as total_commission,
                COUNT(c.id) as commission_count
            FROM users u
            INNER JOIN commissions c ON u.id = c.user_id
            WHERE 1=1
        ";
        
        $params = [];
        
        if ($type) {
            $sql .= " AND c.type = ?";
            $params[] = $type;
        }
        
        $sql .= "
            GROUP BY u.id, u.name, u.email
            ORDER BY total_commission DESC
            LIMIT ?
        ";
        
        $params[] = $limit;
        
        return $this->query($sql, $params);
    }
    
    /**
     * Get commission summary for admin
     */
    public function getCommissionSummary($dateFrom = null, $dateTo = null) {
        $sql = "
            SELECT 
                type,
                COUNT(*) as count,
                SUM(amount) as total,
                AVG(amount) as average,
                MIN(amount) as minimum,
                MAX(amount) as maximum
            FROM commissions 
            WHERE 1=1
        ";
        
        $params = [];
        
        if ($dateFrom) {
            $sql .= " AND created_at >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $sql .= " AND created_at <= ?";
            $params[] = $dateTo;
        }
        
        $sql .= " GROUP BY type ORDER BY total DESC";
        
        return $this->query($sql, $params);
    }
    
    /**
     * Get recent commissions for dashboard
     */
    public function getRecentCommissions($limit = 10) {
        $stmt = $this->db->prepare("
            SELECT c.*, u.name as user_name, fu.name as from_user_name
            FROM commissions c
            INNER JOIN users u ON c.user_id = u.id
            INNER JOIN users fu ON c.from_user_id = fu.id
            ORDER BY c.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    }
    
    /**
     * Calculate referral commission
     */
    public function calculateReferralCommission($sponsorId, $newUserId, $amount) {
        // Calculate referral commission (e.g., 5% of first purchase)
        $commissionRate = 0.05; // 5%
        $commissionAmount = $amount * $commissionRate;
        
        if ($commissionAmount > 0) {
            return $this->recordCommission(
                $sponsorId,
                $newUserId,
                'referral',
                $commissionAmount,
                'Referral commission from new user registration'
            );
        }
        
        return false;
    }
    
    /**
     * Get pairing history with BV details
     */
    public function getPairingHistory($userId, $limit = 50) {
        $stmt = $this->db->prepare("
            SELECT *
            FROM commissions 
            WHERE user_id = ? AND type = 'pairing'
            ORDER BY created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        return $stmt->fetchAll();
    }
}
?>
