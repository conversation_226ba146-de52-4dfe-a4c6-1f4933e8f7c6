<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">
                <i class="fas fa-shopping-cart"></i> Product Catalog
            </h1>
            <p class="text-muted">Discover our premium health and wellness products</p>
        </div>
        <div class="col-md-6">
            <!-- Search and Filter -->
            <form method="GET" class="d-flex gap-2">
                <input type="hidden" name="page" value="product">
                <input type="hidden" name="action" value="catalog">
                
                <div class="input-group">
                    <input type="text" name="search" class="form-control" placeholder="Search products..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="btn btn-outline-secondary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Filter Options -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <input type="hidden" name="page" value="product">
                        <input type="hidden" name="action" value="catalog">
                        
                        <div class="col-md-3">
                            <label for="price_min" class="form-label">Min Price (₹)</label>
                            <input type="number" class="form-control" id="price_min" name="price_min" 
                                   value="<?php echo $priceMin; ?>" min="0" step="0.01">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="price_max" class="form-label">Max Price (₹)</label>
                            <input type="number" class="form-control" id="price_max" name="price_max" 
                                   value="<?php echo $priceMax; ?>" min="0" step="0.01">
                        </div>
                        
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> Apply Filter
                            </button>
                            <a href="?page=product&action=catalog" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                        
                        <div class="col-md-3 text-md-end">
                            <?php if (isLoggedIn()): ?>
                                <a href="<?php echo SITE_URL; ?>?page=product&action=cart" class="btn btn-success">
                                    <i class="fas fa-shopping-cart"></i> Cart 
                                    <span class="badge bg-light text-dark" id="cartCount">0</span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Products Grid -->
    <?php if (!empty($products)): ?>
        <div class="row g-4 mb-4">
            <?php foreach ($products as $product): ?>
                <div class="col-lg-3 col-md-4 col-sm-6">
                    <div class="card product-card h-100">
                        <div class="position-relative">
                            <img src="<?php echo $product['image_url']; ?>" 
                                 class="card-img-top product-image" 
                                 alt="<?php echo htmlspecialchars($product['name']); ?>">
                            <div class="bv-badge">
                                <?php echo $product['bv']; ?> BV
                            </div>
                        </div>
                        
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                            <p class="card-text text-muted flex-grow-1">
                                <?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>
                                <?php if (strlen($product['description']) > 100): ?>...<?php endif; ?>
                            </p>
                            
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h4 class="text-primary mb-0"><?php echo formatCurrency($product['price']); ?></h4>
                                        <small class="text-muted">BV: <?php echo $product['bv']; ?></small>
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <a href="<?php echo SITE_URL; ?>?page=product&action=details&id=<?php echo $product['id']; ?>" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> View Details
                                    </a>
                                    
                                    <?php if (isLoggedIn()): ?>
                                        <form method="POST" action="<?php echo SITE_URL; ?>?page=product&action=addToCart" class="add-to-cart-form">
                                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                            <input type="hidden" name="quantity" value="1">
                                            <button type="submit" class="btn btn-success btn-sm">
                                                <i class="fas fa-cart-plus"></i> Add to Cart
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <a href="<?php echo SITE_URL; ?>?page=auth&action=login" class="btn btn-success btn-sm">
                                            <i class="fas fa-sign-in-alt"></i> Login to Buy
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($pagination['total_pages'] > 1): ?>
            <div class="row">
                <div class="col-12">
                    <nav aria-label="Product pagination">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_prev']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=product&action=catalog&page=<?php echo $pagination['prev_page']; ?>&search=<?php echo urlencode($search); ?>&price_min=<?php echo $priceMin; ?>&price_max=<?php echo $priceMax; ?>">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=product&action=catalog&page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&price_min=<?php echo $priceMin; ?>&price_max=<?php echo $priceMax; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=product&action=catalog&page=<?php echo $pagination['next_page']; ?>&search=<?php echo urlencode($search); ?>&price_min=<?php echo $priceMin; ?>&price_max=<?php echo $priceMax; ?>">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    
                    <div class="text-center">
                        <small class="text-muted">
                            Showing <?php echo (($pagination['current_page'] - 1) * $pagination['items_per_page']) + 1; ?> 
                            to <?php echo min($pagination['current_page'] * $pagination['items_per_page'], $pagination['total_items']); ?> 
                            of <?php echo number_format($pagination['total_items']); ?> products
                        </small>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
    <?php else: ?>
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-box text-muted fa-3x mb-3"></i>
                    <h5 class="text-muted">No products found</h5>
                    <?php if ($search || $priceMin || $priceMax): ?>
                        <p class="text-muted">No products match your search criteria</p>
                        <a href="?page=product&action=catalog" class="btn btn-outline-primary">
                            <i class="fas fa-times"></i> Clear Filters
                        </a>
                    <?php else: ?>
                        <p class="text-muted">Products will be available soon</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Features Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h5 class="card-title text-center mb-4">
                        <i class="fas fa-star text-warning"></i> Why Choose Our Products?
                    </h5>
                    <div class="row g-4">
                        <div class="col-md-3 text-center">
                            <i class="fas fa-leaf text-success fa-2x mb-2"></i>
                            <h6>100% Natural</h6>
                            <small class="text-muted">Made from premium natural ingredients</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <i class="fas fa-certificate text-primary fa-2x mb-2"></i>
                            <h6>Certified Quality</h6>
                            <small class="text-muted">All products are quality certified</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <i class="fas fa-shipping-fast text-info fa-2x mb-2"></i>
                            <h6>Fast Delivery</h6>
                            <small class="text-muted">Quick and secure delivery</small>
                        </div>
                        <div class="col-md-3 text-center">
                            <i class="fas fa-coins text-warning fa-2x mb-2"></i>
                            <h6>Earn BV</h6>
                            <small class="text-muted">Every purchase earns Business Volume</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Update cart count on page load
    updateCartCount();
    
    // Handle add to cart forms
    $('.add-to-cart-form').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const button = form.find('button');
        const originalText = button.html();
        
        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin"></i> Adding...');
        button.prop('disabled', true);
        
        // Submit form
        $.post(form.attr('action'), form.serialize())
            .done(function() {
                showToast('Product added to cart!', 'success');
                updateCartCount();
                
                // Reset button
                button.html('<i class="fas fa-check"></i> Added!');
                setTimeout(function() {
                    button.html(originalText);
                    button.prop('disabled', false);
                }, 2000);
            })
            .fail(function() {
                showToast('Failed to add product to cart', 'danger');
                button.html(originalText);
                button.prop('disabled', false);
            });
    });
});

function updateCartCount() {
    <?php if (isLoggedIn()): ?>
        $.get('<?php echo SITE_URL; ?>?page=product&action=getCartCount')
            .done(function(data) {
                $('#cartCount').text(data.count || 0);
            });
    <?php endif; ?>
}
</script>
